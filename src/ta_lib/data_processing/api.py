"""Useful utilities for feature engineering.

The module provides access to some useful utilities around feature selection
and ``sklearn`` style feature transformers.
"""

import numpy as np
import pandas as pd
from sklearn.base import BaseEstimator, TransformerMixin


class Outlier(BaseEstimator, TransformerMixin):
    """Transformer to treat outliers by either capping them or dropping them.

    Parameters
    ----------
    method: str, default is percentile.
        Accepted values are mean, median, percentile and threshold.
        The method that is used to determine the upper/lower bounds.
    lb: dict or float, default is None.
        Lower bound values. If None, will be computed based on method.
    ub: dict or float, default is None.
        Upper bound values. If None, will be computed based on method.
    """

    def __init__(self, method="percentile", lb=None, ub=None):
        """Initialize Outlier transformer."""
        if method not in ["mean", "median", "percentile", "threshold"]:
            raise ValueError(
                "Unsupported outlier method '{}', should be "
                "one of from ['mean', 'median', 'percentile', "
                "'threshold']".format(method)
            )

        default_limits = {
            "percentile": (0.01, 0.99),
            "mean": (3, 3),
            "median": (1.5, 1.5),
            "threshold": ({}, {}),
        }

        self.method = method
        if lb is None:
            self.lb = default_limits[method][0]
        else:
            self.lb = lb

        if ub is None:
            self.ub = default_limits[method][1]
        else:
            self.ub = ub

    def fit(self, X, cols=None):
        """Compute outlier limits from X.

        Parameters
        ----------
        X : pd.DataFrame or np.Array
            Dataframe/2D Array consisting of independent features
        cols : list, optional
            List of column names for features relevant when
            X is Arrays, by default None
        """
        (self.lb, self.ub) = self._compute_outlier_bounds(
            X, cols, self.method, self.lb, self.ub
        )
        return self

    def transform(self, X, drop=False):
        """Treat outliers for X.

        Parameters
        ----------
        X : pd.DataFrame or np.Array
            Dataframe/2D Array consisting of independent features.
        drop: bool, default is False
            If True, records contains outlier will be dropped.

        Returns
        -------
        pd.DataFrame
            Transformed dataframe is returned
        """
        df = X.copy()
        for (col, lb) in self.lb.items():
            fil_lower = df[col] < lb
            if drop is True:
                df = df[~fil_lower]
            else:
                df.loc[fil_lower, col] = lb

        for (col, ub) in self.ub.items():
            fil_upper = df[col] > ub
            if drop is True:
                df = df[~fil_upper]
            else:
                df.loc[fil_upper, col] = ub
        return df

    def fit_transform(self, X, drop=False, cols=None):
        """Fit to data, then transform it.

        Parameters
        ----------
        X: pd.DataFrame or np.Array
            Dataframe/2D Array consisting of independent features
        drop: bool, default is False
            If True, records contains outlier will be dropped.
        cols: list, optional
            List of column names of features, by default None

        Returns
        -------
        pd.DataFrame
            Transformed dataframe is returned
        """
        return self.fit(X, cols).transform(X, drop)

    def _compute_outlier_bounds(self, X, cols, method, lb, ub):
        """Compute outlier bounds based on the specified method."""
        if isinstance(X, np.ndarray):
            X = pd.DataFrame(X, columns=cols)

        if cols is None:
            cols = X.select_dtypes(include=[np.number]).columns.tolist()

        lower_bounds = {}
        upper_bounds = {}

        for col in cols:
            if col not in X.columns:
                continue

            data = X[col].dropna()

            if method == "percentile":
                lower_bounds[col] = data.quantile(lb if isinstance(lb, float) else 0.01)
                upper_bounds[col] = data.quantile(ub if isinstance(ub, float) else 0.99)
            elif method == "mean":
                mean_val = data.mean()
                std_val = data.std()
                lower_bounds[col] = mean_val - (lb if isinstance(lb, float) else 3) * std_val
                upper_bounds[col] = mean_val + (ub if isinstance(ub, float) else 3) * std_val
            elif method == "median":
                median_val = data.median()
                q1 = data.quantile(0.25)
                q3 = data.quantile(0.75)
                iqr = q3 - q1
                lower_bounds[col] = q1 - (lb if isinstance(lb, float) else 1.5) * iqr
                upper_bounds[col] = q3 + (ub if isinstance(ub, float) else 1.5) * iqr
            elif method == "threshold":
                if isinstance(lb, dict) and col in lb:
                    lower_bounds[col] = lb[col]
                else:
                    lower_bounds[col] = data.min()

                if isinstance(ub, dict) and col in ub:
                    upper_bounds[col] = ub[col]
                else:
                    upper_bounds[col] = data.max()

        return lower_bounds, upper_bounds

