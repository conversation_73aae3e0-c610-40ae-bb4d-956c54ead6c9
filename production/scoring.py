"""Processors for the model scoring/evaluation step of the worklow."""
import os.path as op
import mlflow
from sklearn.metrics import mean_squared_error, r2_score

from ta_lib.core.api import (
    get_dataframe,
    get_feature_names_from_column_transformer,
    get_package_path, 
    hash_object, 
    load_dataset,
    load_pipeline, 
    register_processor, 
    save_dataset, 
    DEFAULT_ARTIFACTS_PATH
)
from production.mlflow_integration import init_mlflow, log_model_metrics, log_model_params, log_model_artifact, get_mlflow_run_url


@register_processor("model-eval", "score-model")
def score_model(context, params):   
    """Score a pre-trained model."""

    input_features_ds = "test/sales/features"
    input_target_ds = "test/sales/target"
    output_ds = "score/sales/output"
    
    artifacts_folder = DEFAULT_ARTIFACTS_PATH
    print(f"Using artifacts folder: {artifacts_folder}")

    # Initialize MLflow
    experiment_name = "housing-price-prediction"
    artifact_location = op.abspath(op.join(artifacts_folder, "mlruns"))
    print(f"MLflow artifact location: {artifact_location}")
    
    client, experiment_id = init_mlflow(experiment_name, artifact_location)
    print(f"MLflow experiment ID: {experiment_id}")
    
    # Start MLflow run
    with mlflow.start_run(run_name="score_model") as run:
        run_id = run.info.run_id
        print(f"Started MLflow run with ID: {run_id}")
        
        # load test datasets
        print("Loading test datasets...")
        test_X = load_dataset(context, input_features_ds)
        test_y = load_dataset(context, input_target_ds)
        print(f"Loaded test data: X shape={test_X.shape}, y shape={test_y.shape}")

        # load the feature pipeline and training pipelines
        print("Loading pipelines...")
        curated_columns = load_pipeline(op.join(artifacts_folder, "curated_columns.joblib"))
        features_transformer = load_pipeline(op.join(artifacts_folder, "features.joblib"))
        model_pipeline = load_pipeline(op.join(artifacts_folder, "train_pipeline.joblib"))
        print("Pipelines loaded successfully")

        # transform the test dataset
        print("Transforming test dataset...")
        test_X = get_dataframe(
            features_transformer.transform(test_X),
            get_feature_names_from_column_transformer(features_transformer),
        )
        test_X = test_X[curated_columns]
        print(f"Transformed test data shape: {test_X.shape}")

        # make a prediction
        print("Making predictions...")
        test_X["yhat"] = model_pipeline.predict(test_X)
        print("Predictions completed")
        
        # Calculate and log metrics
        print("Calculating metrics...")
        mse = mean_squared_error(test_y, test_X["yhat"])
        r2 = r2_score(test_y, test_X["yhat"])
        
        metrics = {
            "test_mse": mse,
            "test_r2": r2
        }
        print(f"Calculated metrics: {metrics}")
        
        # Log metrics to MLflow
        print("Logging metrics to MLflow...")
        log_model_metrics(metrics)
        
        # Log parameters
        params = {
            "model_type": "regression",
            "test_size": 0.2,
            "random_state": 42
        }
        log_model_params(params)
        
        # Log the predictions as an artifact
        print("Saving predictions and logging as artifact...")
        predictions_path = op.abspath(op.join(artifacts_folder, "predictions.csv"))
        test_X.to_csv(predictions_path, index=False)
        print(f"Saved predictions to {predictions_path}")
        
        log_model_artifact(predictions_path, "predictions")
        
        # store the predictions for any further processing
        save_dataset(context, test_X, output_ds)
        print(f"Saved predictions to dataset: {output_ds}")
        
        # Print the URL to view the run in the MLflow UI
        run_url = get_mlflow_run_url(experiment_id, run_id)
        print(f"MLflow run completed: {run_id}")
        print(f"View this run at: {run_url}")
        
        return {"metrics": metrics}
