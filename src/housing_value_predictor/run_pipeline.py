"""
Main script to run the entire housing value prediction pipeline.

This script orchestrates the complete ML pipeline:
1. Data preparation (ingest_data.py)
2. Model training (train.py)
3. Model scoring (score.py)

All steps are tracked using MLflow, with each step running as a child run
under a parent MLflow run.
"""
import argparse
import logging
import os
import subprocess
import sys
from typing import List

import mlflow

from housing_value_predictor.config import (
    ARTIFACTS_DIR,
    DEFAULT_LOG_LEVEL,
    PROCESSED_DATA_DIR,
)
from housing_value_predictor.utils import setup_logging


def run_command(command: List[str], logger: logging.Logger, env_vars: dict = None) -> int:
    """Run a command as a subprocess and log its output.

    Args:
        command (List[str]): Command to run as a list of strings.
        logger (logging.Logger): Logger instance for logging messages.
        env_vars (dict, optional): Environment variables to pass to the subprocess.

    Returns:
        int: Return code of the command.
    """
    logger.info(f"Running command: {' '.join(command)}")

    # Set up environment variables
    env = os.environ.copy()
    if env_vars:
        env.update(env_vars)
        logger.info(f"Added environment variables: {env_vars}")

    process = subprocess.Popen(
        command,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        universal_newlines=True,
        env=env,
    )

    # Stream output to logger
    for line in process.stdout:
        logger.info(line.strip())

    return_code = process.wait()
    if return_code != 0:
        logger.error(f"Command failed with return code {return_code}")
    else:
        logger.info("Command completed successfully")

    return return_code


def main(args: argparse.Namespace) -> None:
    """Main function to run the entire pipeline.

    Orchestrates the complete ML pipeline:
    1. Sets up logging.
    2. Creates a parent MLflow run.
    3. Runs data preparation as a child run.
    4. Runs model training as a child run.
    5. Runs model scoring as a child run.

    Args:
        args (argparse.Namespace): Parsed command-line arguments containing:
            data_dir (str): Directory for processed datasets.
            model_dir (str): Directory for model artifacts.
            predictions_dir (str): Directory for predictions and metrics.
            log_level (str): Logging level.
            log_path (Optional[str]): Path to the log file.
            no_console_log (bool): Whether to disable console logging.
    """
    # Setup logging
    logger = setup_logging(
        log_level=args.log_level,
        log_file=args.log_path,
        console_log=not args.no_console_log,
    )

    # Get MLflow tracking URI
    tracking_uri = mlflow.get_tracking_uri()
    logger.info(f"MLflow tracking URI: {tracking_uri}")

    # Set environment variables for child processes
    mlflow_env_vars = {
        "MLFLOW_TRACKING_URI": tracking_uri,
    }

    # Start parent MLflow run
    with mlflow.start_run(run_name="housing_prediction_pipeline") as parent_run:
        parent_run_id = parent_run.info.run_id
        logger.info(f"Started parent MLflow run with ID: {parent_run_id}")

        # Log pipeline parameters
        mlflow.log_param("data_dir", args.data_dir)
        mlflow.log_param("model_dir", args.model_dir)
        mlflow.log_param("predictions_dir", args.predictions_dir)

        # Log MLflow configuration for debugging
        mlflow.log_param("mlflow_tracking_uri", tracking_uri)

        # Step 1: Data preparation
        logger.info("Starting data preparation step")
        # Get the absolute path to the ingest_data.py script
        ingest_data_path = os.path.join(
            os.path.dirname(os.path.abspath(__file__)),
            "ingest_data.py"
        )
        data_prep_cmd = [
            sys.executable,
            ingest_data_path,
            "--output-dir", args.data_dir,
            "--log-level", args.log_level,
            "--parent-run-id", parent_run_id,
        ]

        # Log this information in the parent run for debugging
        mlflow.log_param("data_prep_script", ingest_data_path)
        mlflow.log_param("data_prep_cmd", " ".join(data_prep_cmd))
        if args.log_path:
            data_prep_cmd.extend(["--log-path", f"{args.log_path}.data_prep"])
        if args.no_console_log:
            data_prep_cmd.append("--no-console-log")

        if run_command(data_prep_cmd, logger, mlflow_env_vars) != 0:
            logger.error("Data preparation step failed. Exiting pipeline.")
            return

        # Step 2: Model training
        logger.info("Starting model training step")
        # Get the absolute path to the train.py script
        train_path = os.path.join(
            os.path.dirname(os.path.abspath(__file__)),
            "train.py"
        )
        train_cmd = [
            sys.executable,
            train_path,
            "--input-dir", args.data_dir,
            "--output-dir", args.model_dir,
            "--log-level", args.log_level,
            "--parent-run-id", parent_run_id,
        ]
        if args.log_path:
            train_cmd.extend(["--log-path", f"{args.log_path}.train"])
        if args.no_console_log:
            train_cmd.append("--no-console-log")

        if run_command(train_cmd, logger, mlflow_env_vars) != 0:
            logger.error("Model training step failed. Exiting pipeline.")
            return

        # Step 3: Model scoring
        logger.info("Starting model scoring step")
        # Get the absolute path to the score.py script
        score_path = os.path.join(
            os.path.dirname(os.path.abspath(__file__)),
            "score.py"
        )
        score_cmd = [
            sys.executable,
            score_path,
            "--input-dir", args.data_dir,
            "--model-dir", args.model_dir,
            "--output-dir", args.predictions_dir,
            "--log-level", args.log_level,
            "--parent-run-id", parent_run_id,
        ]
        if args.log_path:
            score_cmd.extend(["--log-path", f"{args.log_path}.score"])
        if args.no_console_log:
            score_cmd.append("--no-console-log")

        if run_command(score_cmd, logger, mlflow_env_vars) != 0:
            logger.error("Model scoring step failed. Exiting pipeline.")
            return

        # Log summary metrics in the parent run
        logger.info("Logging summary metrics in parent run")
        try:
            # Get the child runs
            client = mlflow.tracking.MlflowClient()
            child_runs = client.search_runs(
                experiment_ids=[mlflow.active_run().info.experiment_id],
                filter_string=f"tags.mlflow.parentRunId = '{parent_run_id}'",
            )

            # Log summary metrics from child runs
            for run in child_runs:
                run_name = run.data.tags.get("mlflow.runName", "unknown")
                logger.info(f"Found child run: {run_name} ({run.info.run_id})")

                # Log key metrics from child runs to parent run
                for key, value in run.data.metrics.items():
                    mlflow.log_metric(f"{run_name}_{key}", value)

            mlflow.log_metric("num_child_runs", len(child_runs))

        except Exception as e:
            logger.error(f"Error logging summary metrics: {e}")

        logger.info("Pipeline completed successfully")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run the complete housing prediction pipeline")
    parser.add_argument(
        "--data-dir",
        type=str,
        default=PROCESSED_DATA_DIR,
        help="Directory for processed datasets",
    )
    parser.add_argument(
        "--model-dir",
        type=str,
        default=ARTIFACTS_DIR,
        help="Directory for model artifacts",
    )
    parser.add_argument(
        "--predictions-dir",
        type=str,
        default=os.path.join(ARTIFACTS_DIR, "predictions"),
        help="Directory for predictions and metrics",
    )
    parser.add_argument(
        "--log-level",
        type=str,
        default=DEFAULT_LOG_LEVEL,
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        help="Set the logging level",
    )
    parser.add_argument(
        "--log-path",
        type=str,
        help="Base path for log files. If specified, each step will append its name to this path",
    )
    parser.add_argument(
        "--no-console-log",
        action="store_true",
        help="Disable logging to console",
    )

    args = parser.parse_args()
    main(args)
