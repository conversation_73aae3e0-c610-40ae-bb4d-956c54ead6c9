# Code in this package is not meant to be modified by end-users of code-templates.
# Simplified version without tigerml dependency for housing price prediction

import pandas as pd
import numpy as np
from statsmodels.stats.outliers_influence import variance_inflation_factor


def calc_vif(df):
    """Calculate Variance Inflation Factor for features."""
    numeric_df = df.select_dtypes(include=[np.number])
    vif_data = pd.DataFrame()
    vif_data["Feature"] = numeric_df.columns
    vif_data["VIF"] = [variance_inflation_factor(numeric_df.values, i)
                       for i in range(len(numeric_df.columns))]
    return vif_data


def detigerify(data):
    """Convert tigerml data format to standard pandas format."""
    if isinstance(data, pd.DataFrame):
        return data
    elif hasattr(data, 'data'):
        return data.data
    else:
        return data


class SimpleAnalyser:
    """Simple analyser replacement for tigerml.eda.Analyser."""

    def __init__(self, df, y=None, y_continuous=None):
        self.df = df
        self.y = y
        self.y_continuous = y_continuous

    def variable_summary(self):
        """Get basic variable summary."""
        summary = pd.DataFrame({
            'Variable': self.df.columns,
            'Type': [str(dtype) for dtype in self.df.dtypes],
            'Unique_Count': [self.df[col].nunique() for col in self.df.columns],
            'Missing_Count': [self.df[col].isnull().sum() for col in self.df.columns]
        })
        return summary


def _get_analyser(df, y=None, y_continuous=None):
    """Get a simple analyser instance."""
    return SimpleAnalyser(df, y=y, y_continuous=y_continuous)
