"""Processors for the data cleaning step of the worklow.

The processors in this step, apply the various cleaning steps identified
during EDA to create the training datasets.
"""
import numpy as np
import pandas as pd
import mlflow
from datetime import datetime
from sklearn.model_selection import StratifiedShuffleSplit

from ta_lib.core.api import (
    custom_train_test_split,
    load_dataset,
    register_processor,
    save_dataset,
    string_cleaning
)
from production.scripts import binned_selling_price
from production.mlflow_integration import (
    init_mlflow,
    log_data_quality_metrics,
    log_dataset_info,
    start_mlflow_run_with_tags,
    log_processing_time
)
from production.mlflow_config import get_experiment_name


@register_processor("data-cleaning", "product")
def clean_product_table(context, params):
    """Clean the ``PRODUCT`` data table.

    The table contains information on the inventory being sold. This
    includes information on inventory id, properties of the item and
    so on.
    """
    from datetime import datetime

    input_dataset = "raw/product"
    output_dataset = "cleaned/product"

    # Initialize MLflow for data cleaning
    experiment_name = get_experiment_name("data-cleaning")
    init_mlflow(experiment_name)

    # Start MLflow run with tags
    with start_mlflow_run_with_tags(
        run_name="clean_product_table",
        job_name="data-cleaning",
        stage_name="product",
        dataset_type="product"
    ):
        start_time = datetime.now()

        # Log parameters
        mlflow.log_param("input_dataset", input_dataset)
        mlflow.log_param("output_dataset", output_dataset)

        # load dataset
        product_df = load_dataset(context, input_dataset)

        # Log input dataset info
        log_dataset_info(product_df, "raw_product")
        log_data_quality_metrics(product_df, "raw_product")

        # Store original columns for later use
        original_cols = product_df.columns.tolist()
        mlflow.log_param("original_columns_count", len(original_cols))

        product_df_clean = (
            product_df
            # set dtypes : nothing to do here
            .passthrough()
            .transform_columns(
                product_df.columns.to_list(), string_cleaning, elementwise=False
            )
            .replace({"": np.nan})
        )

        # Handle coalesce manually since delete_columns is not supported
        columns_merged = []
        if 'color' in product_df_clean.columns and 'Ext_Color' in product_df_clean.columns:
            product_df_clean['color'] = product_df_clean['color'].fillna(product_df_clean['Ext_Color'])
            product_df_clean = product_df_clean.drop(columns=['Ext_Color'])
            columns_merged.append("color <- Ext_Color")

        if 'MemorySize' in product_df_clean.columns and 'Ext_memorySize' in product_df_clean.columns:
            product_df_clean['memory_size'] = product_df_clean['MemorySize'].fillna(product_df_clean['Ext_memorySize'])
            product_df_clean = product_df_clean.drop(columns=['MemorySize', 'Ext_memorySize'])
            columns_merged.append("memory_size <- MemorySize, Ext_memorySize")

        # Log column merging operations
        if columns_merged:
            mlflow.log_param("columns_merged", "; ".join(columns_merged))

        # ensure that the key column does not have duplicate records
        duplicates_before = product_df_clean.duplicated(subset=['SKU']).sum()
        product_df_clean = product_df_clean.drop_duplicates(subset=['SKU'], keep='first')
        duplicates_removed = duplicates_before

        # clean column names
        product_df_clean = product_df_clean.clean_names(case_type="snake")

        # Log cleaning metrics
        cleaning_metrics = {
            "duplicates_removed": duplicates_removed,
            "columns_after_cleaning": len(product_df_clean.columns),
            "rows_after_cleaning": len(product_df_clean)
        }
        mlflow.log_metrics(cleaning_metrics)

        # Log output dataset info
        log_dataset_info(product_df_clean, "cleaned_product")
        log_data_quality_metrics(product_df_clean, "cleaned_product")

        # Log processing time
        end_time = datetime.now()
        log_processing_time(start_time, end_time, "product_cleaning")

        # save the dataset
        save_dataset(context, product_df_clean, output_dataset)

        return product_df_clean


@register_processor("data-cleaning", "orders")
def clean_order_table(context, params):
    """Clean the ``ORDER`` data table.

    The table containts the sales data and has information on the invoice,
    the item purchased, the price etc.
    """
    from datetime import datetime

    input_dataset = "raw/orders"
    output_dataset = "cleaned/orders"

    # Initialize MLflow for data cleaning
    experiment_name = get_experiment_name("data-cleaning")
    init_mlflow(experiment_name)

    # Start MLflow run with tags
    with start_mlflow_run_with_tags(
        run_name="clean_order_table",
        job_name="data-cleaning",
        stage_name="orders",
        dataset_type="orders"
    ):
        start_time = datetime.now()

        # Log parameters
        mlflow.log_param("input_dataset", input_dataset)
        mlflow.log_param("output_dataset", output_dataset)

        # load dataset
        orders_df = load_dataset(context, input_dataset)

        # Log input dataset info
        log_dataset_info(orders_df, "raw_orders")
        log_data_quality_metrics(orders_df, "raw_orders")

        # list of columns that we want string cleaning op to be performed on.
        str_cols = list(
            set(orders_df.select_dtypes("object").columns.to_list())
            - set(["Quantity", "InvoiceNo", "Orderno", "LedgerDate"])
        )

        # Log transformation parameters
        mlflow.log_param("string_columns_to_clean", len(str_cols))
        mlflow.log_param("type_conversion_columns", ["Quantity", "InvoiceNo", "Orderno"])
        mlflow.log_param("datetime_column", "LedgerDate")

        orders_df_clean = (
            orders_df
            # set dtypes
            .change_type(["Quantity", "InvoiceNo", "Orderno"], np.int64)
            # set dtypes
            .to_datetime("LedgerDate", format="%d/%m/%Y")
            # clean string columns (NOTE: only handling datetime columns)
            .transform_columns(str_cols, string_cleaning, elementwise=False)
            # clean column names
            .clean_names(case_type="snake").rename_columns({"orderno": "order_no"})
        )

        # Log cleaning metrics
        cleaning_metrics = {
            "columns_after_cleaning": len(orders_df_clean.columns),
            "rows_after_cleaning": len(orders_df_clean),
            "string_columns_processed": len(str_cols)
        }
        mlflow.log_metrics(cleaning_metrics)

        # Log output dataset info
        log_dataset_info(orders_df_clean, "cleaned_orders")
        log_data_quality_metrics(orders_df_clean, "cleaned_orders")

        # Log processing time
        end_time = datetime.now()
        log_processing_time(start_time, end_time, "orders_cleaning")

        # save dataset
        save_dataset(context, orders_df_clean, output_dataset)
        return orders_df_clean


@register_processor("data-cleaning", "sales")
def clean_sales_table(context, params):
    """Clean the ``SALES`` data table.

    The table is a summary table obtained by doing a ``inner`` join of the
    ``PRODUCT`` and ``ORDERS`` tables.
    """
    from datetime import datetime

    input_product_ds = "cleaned/product"
    input_orders_ds = "cleaned/orders"
    output_dataset = "cleaned/sales"

    # Initialize MLflow for data cleaning
    experiment_name = get_experiment_name("data-cleaning")
    init_mlflow(experiment_name)

    # Start MLflow run with tags
    with start_mlflow_run_with_tags(
        run_name="clean_sales_table",
        job_name="data-cleaning",
        stage_name="sales",
        dataset_type="sales"
    ):
        start_time = datetime.now()

        # Log parameters
        mlflow.log_param("input_product_dataset", input_product_ds)
        mlflow.log_param("input_orders_dataset", input_orders_ds)
        mlflow.log_param("output_dataset", output_dataset)
        mlflow.log_param("join_type", "inner")
        mlflow.log_param("join_key", "sku")

        # load datasets
        product_df = load_dataset(context, input_product_ds)
        orders_df = load_dataset(context, input_orders_ds)

        # Log input dataset info
        log_dataset_info(product_df, "input_product")
        log_dataset_info(orders_df, "input_orders")
        log_data_quality_metrics(product_df, "input_product")
        log_data_quality_metrics(orders_df, "input_orders")

        # Perform merge
        sales_df_clean = orders_df.merge(product_df, how="inner", on="sku")

        # Log merge metrics
        merge_metrics = {
            "product_rows": len(product_df),
            "orders_rows": len(orders_df),
            "merged_rows": len(sales_df_clean),
            "merge_efficiency": len(sales_df_clean) / len(orders_df) if len(orders_df) > 0 else 0
        }
        mlflow.log_metrics(merge_metrics)

        # Log output dataset info
        log_dataset_info(sales_df_clean, "cleaned_sales")
        log_data_quality_metrics(sales_df_clean, "cleaned_sales")

        # Log processing time
        end_time = datetime.now()
        log_processing_time(start_time, end_time, "sales_cleaning")

        save_dataset(context, sales_df_clean, output_dataset)
        return sales_df_clean


@register_processor("data-cleaning", "train-test")
def create_training_datasets(context, params):
    """Split the ``SALES`` table into ``train`` and ``test`` datasets."""
    from datetime import datetime

    input_dataset = "cleaned/sales"
    output_train_features = "train/sales/features"
    output_train_target = "train/sales/target"
    output_test_features = "test/sales/features"
    output_test_target = "test/sales/target"

    # Initialize MLflow for data cleaning
    experiment_name = get_experiment_name("data-cleaning")
    init_mlflow(experiment_name)

    # Start MLflow run with tags
    with start_mlflow_run_with_tags(
        run_name="create_training_datasets",
        job_name="data-cleaning",
        stage_name="train-test",
        dataset_type="train_test_split"
    ):
        start_time = datetime.now()

        # Log parameters
        mlflow.log_param("input_dataset", input_dataset)
        mlflow.log_param("test_size", params["test_size"])
        mlflow.log_param("target_column", params["target"])
        mlflow.log_param("random_seed", context.random_seed)
        mlflow.log_param("split_strategy", "StratifiedShuffleSplit")

        # load dataset
        sales_df_processed = load_dataset(context, input_dataset)

        # Log input dataset info
        log_dataset_info(sales_df_processed, "input_sales")
        log_data_quality_metrics(sales_df_processed, "input_sales")

        # creating additional features that are not affected by train test split. These are features that are processed globally
        # first time customer(02_data_processing.ipynb)################
        cust_details = (
            sales_df_processed.groupby(["customername"])
            .agg({"ledger_date": "min"})
            .reset_index()
        )
        cust_details.columns = ["customername", "ledger_date"]
        cust_details["first_time_customer"] = 1
        sales_df_processed = sales_df_processed.merge(
            cust_details, on=["customername", "ledger_date"], how="left"
        )
        sales_df_processed["first_time_customer"].fillna(0, inplace=True)

        # days since last purchas(02_data_processing.ipynb)###########
        sales_df_processed.sort_values("ledger_date", inplace=True)
        sales_df_processed["days_since_last_purchase"] = (
            sales_df_processed.groupby("customername")["ledger_date"]
            .diff()
            .dt.days.fillna(0, downcast="infer")
        )

        # Log feature engineering metrics
        unique_customers = sales_df_processed["customername"].nunique()
        first_time_customers = sales_df_processed["first_time_customer"].sum()

        feature_metrics = {
            "unique_customers": unique_customers,
            "first_time_customers": first_time_customers,
            "repeat_customers": unique_customers - first_time_customers,
            "features_added": 2  # first_time_customer, days_since_last_purchase
        }
        mlflow.log_metrics(feature_metrics)

        # split the data
        splitter = StratifiedShuffleSplit(
            n_splits=1, test_size=params["test_size"], random_state=context.random_seed
        )
        sales_df_train, sales_df_test = custom_train_test_split(
            sales_df_processed, splitter, by=binned_selling_price
        )

        # Log split metrics
        split_metrics = {
            "total_rows": len(sales_df_processed),
            "train_rows": len(sales_df_train),
            "test_rows": len(sales_df_test),
            "train_percentage": len(sales_df_train) / len(sales_df_processed) * 100,
            "test_percentage": len(sales_df_test) / len(sales_df_processed) * 100
        }
        mlflow.log_metrics(split_metrics)

        # split train dataset into features and target
        target_col = params["target"]
        train_X, train_y = (
            sales_df_train
            # split the dataset to train and test
            .get_features_targets(target_column_names=target_col)
        )

        # split test dataset into features and target
        test_X, test_y = (
            sales_df_test
            # split the dataset to train and test
            .get_features_targets(target_column_names=target_col)
        )

        # Log dataset info for all splits
        log_dataset_info(train_X, "train_features")
        log_dataset_info(train_y, "train_target")
        log_dataset_info(test_X, "test_features")
        log_dataset_info(test_y, "test_target")

        log_data_quality_metrics(train_X, "train_features")
        log_data_quality_metrics(test_X, "test_features")

        # save the datasets
        save_dataset(context, train_X, output_train_features)
        save_dataset(context, train_y, output_train_target)
        save_dataset(context, test_X, output_test_features)
        save_dataset(context, test_y, output_test_target)

        # Log processing time
        end_time = datetime.now()
        log_processing_time(start_time, end_time, "train_test_split")

        return {
            "train_shape": train_X.shape,
            "test_shape": test_X.shape,
            "target_column": target_col
        }
