"""MLflow integration for production workflows."""
import os
import os.path as op
import mlflow
import pandas as pd
import numpy as np
from datetime import datetime
from ta_lib.core.api import DEFAULT_ARTIFACTS_PATH

# Default MLflow configuration
DEFAULT_EXPERIMENT_NAME = "housing-price-prediction"
DEFAULT_ARTIFACT_LOCATION = op.abspath(op.join(DEFAULT_ARTIFACTS_PATH, "mlruns"))

def init_mlflow(experiment_name=None, artifact_location=None):
    """
    Initialize MLflow tracking.

    Parameters:
        experiment_name (str, optional): Name of the MLflow experiment
        artifact_location (str, optional): Location to store artifacts

    Returns:
        tuple: (MLflow client, experiment ID)
    """
    if experiment_name is None:
        experiment_name = DEFAULT_EXPERIMENT_NAME
    if artifact_location is None:
        artifact_location = DEFAULT_ARTIFACT_LOCATION

    print(f"Initializing MLflow with experiment: {experiment_name}")
    print(f"Artifact location: {artifact_location}")

    # Ensure artifact location directory exists
    os.makedirs(artifact_location, exist_ok=True)

    # Get current tracking URI
    tracking_uri = mlflow.get_tracking_uri()
    print(f"Current MLflow tracking URI: {tracking_uri}")

    # Create MLflow client
    client = mlflow.tracking.MlflowClient()

    # Check if experiment exists
    experiment = mlflow.get_experiment_by_name(experiment_name)

    if experiment is None:
        print(f"Creating new experiment: {experiment_name}")
        experiment_id = mlflow.create_experiment(
            name=experiment_name,
            artifact_location=artifact_location
        )
        print(f"Created experiment with ID: {experiment_id}")
    else:
        experiment_id = experiment.experiment_id
        print(f"Using existing experiment with ID: {experiment_id}")

    mlflow.set_experiment(experiment_name)
    print("MLflow initialization complete")
    return client, experiment_id

def log_model_metrics(metrics_dict):
    """
    Log model metrics to MLflow.
    
    Parameters:
        metrics_dict (dict): Dictionary of metrics to log
    """
    print(f"Logging metrics to MLflow: {metrics_dict}")
    for metric_name, metric_value in metrics_dict.items():
        mlflow.log_metric(metric_name, metric_value)
        print(f"Logged metric: {metric_name} = {metric_value}")

def log_model_params(params_dict):
    """
    Log model parameters to MLflow.
    
    Parameters:
        params_dict (dict): Dictionary of parameters to log
    """
    print(f"Logging parameters to MLflow: {params_dict}")
    for param_name, param_value in params_dict.items():
        mlflow.log_param(param_name, param_value)
        print(f"Logged parameter: {param_name} = {param_value}")

def log_model_artifact(artifact_path, artifact_dir=None):
    """
    Log model artifact to MLflow.
    
    Parameters:
        artifact_path (str): Path to the artifact file
        artifact_dir (str, optional): Directory in MLflow to store the artifact
    """
    print(f"Logging artifact to MLflow: {artifact_path}")
    mlflow.log_artifact(artifact_path, artifact_dir)
    print(f"Logged artifact: {artifact_path}")

def get_mlflow_run_url(experiment_id, run_id):
    """
    Get the URL to view the MLflow run in the UI.
    
    Parameters:
        experiment_id (str): Experiment ID
        run_id (str): Run ID
        
    Returns:
        str: URL to view the run in the MLflow UI
    """
    tracking_uri = mlflow.get_tracking_uri()
    if tracking_uri.startswith("file:"):
        # For local file-based tracking, we need to use localhost
        return f"http://localhost:5000/#/experiments/{experiment_id}/runs/{run_id}"
    else:
        # For remote tracking servers
        return f"{tracking_uri}/#/experiments/{experiment_id}/runs/{run_id}"

def log_sklearn_model(model, model_name="model"):
    """
    Log scikit-learn model to MLflow.

    Parameters:
        model: Trained scikit-learn model
        model_name (str): Name to register the model as
    """
    mlflow.sklearn.log_model(model, model_name)
    return model_name

def log_data_quality_metrics(df, stage_name="data_processing"):
    """
    Log data quality metrics for a DataFrame or Series.

    Parameters:
        df (pd.DataFrame or pd.Series): DataFrame or Series to analyze
        stage_name (str): Name of the processing stage
    """

    # Handle both DataFrame and Series
    if isinstance(df, pd.Series):
        num_columns = 1
        missing_values_total = df.isnull().sum()
        missing_values_percentage = (missing_values_total / len(df)) * 100
        duplicate_rows = df.duplicated().sum()
        memory_usage_mb = df.memory_usage(deep=True) / 1024 / 1024

        # Check if numeric or categorical
        if pd.api.types.is_numeric_dtype(df):
            numeric_columns = 1
            categorical_columns = 0
        else:
            numeric_columns = 0
            categorical_columns = 1
    else:
        num_columns = len(df.columns)
        missing_values_total = df.isnull().sum().sum()
        missing_values_percentage = (missing_values_total / (len(df) * num_columns)) * 100
        duplicate_rows = df.duplicated().sum()
        memory_usage_mb = df.memory_usage(deep=True).sum() / 1024 / 1024

        # Log numeric column statistics
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        numeric_columns = len(numeric_cols)

        # Log categorical column statistics
        categorical_cols = df.select_dtypes(include=['object']).columns
        categorical_columns = len(categorical_cols)

    metrics = {
        f"{stage_name}_num_rows": len(df),
        f"{stage_name}_num_columns": num_columns,
        f"{stage_name}_missing_values_total": missing_values_total,
        f"{stage_name}_missing_values_percentage": missing_values_percentage,
        f"{stage_name}_duplicate_rows": duplicate_rows,
        f"{stage_name}_memory_usage_mb": memory_usage_mb
    }

    if numeric_columns > 0:
        metrics[f"{stage_name}_numeric_columns"] = numeric_columns

    if categorical_columns > 0:
        metrics[f"{stage_name}_categorical_columns"] = categorical_columns

    log_model_metrics(metrics)
    return metrics

def log_dataset_info(df, dataset_name):
    """
    Log basic dataset information.

    Parameters:
        df (pd.DataFrame or pd.Series): Dataset to log info for
        dataset_name (str): Name of the dataset
    """

    # Handle both DataFrame and Series
    if isinstance(df, pd.Series):
        shape_str = f"{df.shape[0]}x1"
        columns = [df.name] if df.name else ["target"]
        dtypes = {df.name or "target": str(df.dtype)}
    else:
        shape_str = f"{df.shape[0]}x{df.shape[1]}"
        columns = list(df.columns)
        dtypes = df.dtypes.to_dict()

    info = {
        f"{dataset_name}_shape": shape_str,
        f"{dataset_name}_columns": columns,
        f"{dataset_name}_dtypes": dtypes
    }

    # Log as parameters (for small info) and artifacts (for larger info)
    mlflow.log_param(f"{dataset_name}_shape", info[f"{dataset_name}_shape"])

    # Save detailed info as artifact
    import tempfile
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write(f"Dataset: {dataset_name}\n")
        f.write(f"Shape: {df.shape}\n")

        if isinstance(df, pd.Series):
            f.write(f"Name: {df.name}\n")
            f.write(f"Data Type: {df.dtype}\n")
            f.write(f"Memory Usage: {df.memory_usage(deep=True)} bytes\n")
        else:
            f.write(f"Columns: {list(df.columns)}\n")
            f.write(f"Data Types:\n{df.dtypes.to_string()}\n")
            f.write(f"Memory Usage:\n{df.memory_usage(deep=True).to_string()}\n")

        temp_path = f.name

    mlflow.log_artifact(temp_path, f"dataset_info/{dataset_name}_info.txt")
    os.unlink(temp_path)  # Clean up temp file

def create_run_tags(job_name, stage_name, **additional_tags):
    """
    Create standardized tags for MLflow runs.

    Parameters:
        job_name (str): Name of the job
        stage_name (str): Name of the stage
        **additional_tags: Additional tags to include

    Returns:
        dict: Dictionary of tags
    """

    tags = {
        "job_name": job_name,
        "stage_name": stage_name,
        "timestamp": datetime.now().isoformat(),
        "environment": "production"
    }
    tags.update(additional_tags)
    return tags

def start_mlflow_run_with_tags(run_name, job_name, stage_name, **additional_tags):
    """
    Start an MLflow run with standardized tags.

    Parameters:
        run_name (str): Name of the run
        job_name (str): Name of the job
        stage_name (str): Name of the stage
        **additional_tags: Additional tags to include

    Returns:
        MLflow run context
    """
    tags = create_run_tags(job_name, stage_name, **additional_tags)
    return mlflow.start_run(run_name=run_name, tags=tags)

def log_processing_time(start_time, end_time, stage_name):
    """
    Log processing time for a stage.

    Parameters:
        start_time (datetime): Start time
        end_time (datetime): End time
        stage_name (str): Name of the processing stage
    """

    if isinstance(start_time, str):
        start_time = datetime.fromisoformat(start_time)
    if isinstance(end_time, str):
        end_time = datetime.fromisoformat(end_time)

    duration_seconds = (end_time - start_time).total_seconds()
    mlflow.log_metric(f"{stage_name}_duration_seconds", duration_seconds)
    mlflow.log_metric(f"{stage_name}_duration_minutes", duration_seconds / 60)

    return duration_seconds
