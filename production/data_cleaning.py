"""Processors for the data cleaning step of the worklow.

The processors in this step, apply the various cleaning steps identified
during EDA to create the training datasets.
"""
import numpy as np
import pandas as pd
import mlflow
from datetime import datetime
from sklearn.model_selection import StratifiedShuffleSplit

from ta_lib.core.api import (
    custom_train_test_split,
    load_dataset,
    register_processor,
    save_dataset,
    string_cleaning
)
from production.scripts import binned_selling_price
from production.mlflow_integration import (
    init_mlflow,
    log_data_quality_metrics,
    log_dataset_info,
    start_mlflow_run_with_tags,
    log_processing_time
)
from production.mlflow_config import get_experiment_name


@register_processor("data-cleaning", "housing")
def clean_housing_table(context, params):
    """Clean the ``HOUSING`` data table.

    The table contains information on housing features like longitude, latitude,
    housing_median_age, total_rooms, total_bedrooms, population, households,
    median_income, median_house_value, and ocean_proximity.
    """

    input_dataset = "raw/housing"
    output_dataset = "cleaned/housing"

    # Initialize MLflow for data cleaning
    experiment_name = get_experiment_name("data-cleaning")
    init_mlflow(experiment_name)

    # Start MLflow run with tags
    with start_mlflow_run_with_tags(
        run_name="clean_housing_table",
        job_name="data-cleaning",
        stage_name="housing",
        dataset_type="housing"
    ):
        start_time = datetime.now()

        # Log parameters
        mlflow.log_param("input_dataset", input_dataset)
        mlflow.log_param("output_dataset", output_dataset)

        # load dataset
        housing_df = load_dataset(context, input_dataset)

        # Log input dataset info
        log_dataset_info(housing_df, "raw_housing")
        log_data_quality_metrics(housing_df, "raw_housing")

        # Store original columns for later use
        original_cols = housing_df.columns.tolist()
        mlflow.log_param("original_columns_count", len(original_cols))

        # Expected housing columns
        expected_cols = [
            'longitude', 'latitude', 'housing_median_age', 'total_rooms',
            'total_bedrooms', 'population', 'households', 'median_income',
            'median_house_value', 'ocean_proximity'
        ]

        # Log column validation
        missing_cols = [col for col in expected_cols if col not in housing_df.columns]
        extra_cols = [col for col in housing_df.columns if col not in expected_cols]

        if missing_cols:
            mlflow.log_param("missing_expected_columns", missing_cols)
        if extra_cols:
            mlflow.log_param("extra_columns", extra_cols)

        # Basic data cleaning for housing data
        housing_df_clean = housing_df.copy()

        # Handle missing values in total_bedrooms (common in housing datasets)
        if 'total_bedrooms' in housing_df_clean.columns:
            missing_bedrooms_before = housing_df_clean['total_bedrooms'].isnull().sum()
            # Fill missing total_bedrooms with median
            housing_df_clean['total_bedrooms'] = housing_df_clean['total_bedrooms'].fillna(
                housing_df_clean['total_bedrooms'].median()
            )
            mlflow.log_param("missing_bedrooms_filled", missing_bedrooms_before)

        # Clean ocean_proximity categorical variable
        if 'ocean_proximity' in housing_df_clean.columns:
            # Clean string values
            housing_df_clean['ocean_proximity'] = housing_df_clean['ocean_proximity'].str.strip().str.upper()
            unique_ocean_values = housing_df_clean['ocean_proximity'].unique().tolist()
            mlflow.log_param("ocean_proximity_categories", unique_ocean_values)

        # Remove any duplicate rows
        duplicates_before = housing_df_clean.duplicated().sum()
        housing_df_clean = housing_df_clean.drop_duplicates()
        duplicates_removed = duplicates_before

        # Create additional features for housing data
        if 'total_rooms' in housing_df_clean.columns and 'households' in housing_df_clean.columns:
            housing_df_clean['rooms_per_household'] = housing_df_clean['total_rooms'] / housing_df_clean['households']

        if 'total_bedrooms' in housing_df_clean.columns and 'households' in housing_df_clean.columns:
            housing_df_clean['bedrooms_per_household'] = housing_df_clean['total_bedrooms'] / housing_df_clean['households']

        if 'population' in housing_df_clean.columns and 'households' in housing_df_clean.columns:
            housing_df_clean['population_per_household'] = housing_df_clean['population'] / housing_df_clean['households']

        # Log cleaning metrics
        cleaning_metrics = {
            "duplicates_removed": duplicates_removed,
            "columns_after_cleaning": len(housing_df_clean.columns),
            "rows_after_cleaning": len(housing_df_clean),
            "features_added": 3  # rooms_per_household, bedrooms_per_household, population_per_household
        }
        mlflow.log_metrics(cleaning_metrics)

        # Log output dataset info
        log_dataset_info(housing_df_clean, "cleaned_housing")
        log_data_quality_metrics(housing_df_clean, "cleaned_housing")

        # Log processing time
        end_time = datetime.now()
        log_processing_time(start_time, end_time, "housing_cleaning")

        # save the dataset
        save_dataset(context, housing_df_clean, output_dataset)

        return housing_df_clean


@register_processor("data-cleaning", "orders")
def clean_order_table(context, params):
    """Clean the ``ORDER`` data table.

    The table containts the sales data and has information on the invoice,
    the item purchased, the price etc.
    """

    input_dataset = "raw/orders"
    output_dataset = "cleaned/orders"

    # Initialize MLflow for data cleaning
    experiment_name = get_experiment_name("data-cleaning")
    init_mlflow(experiment_name)

    # Start MLflow run with tags
    with start_mlflow_run_with_tags(
        run_name="clean_order_table",
        job_name="data-cleaning",
        stage_name="orders",
        dataset_type="orders"
    ):
        start_time = datetime.now()

        # Log parameters
        mlflow.log_param("input_dataset", input_dataset)
        mlflow.log_param("output_dataset", output_dataset)

        # load dataset
        orders_df = load_dataset(context, input_dataset)

        # Log input dataset info
        log_dataset_info(orders_df, "raw_orders")
        log_data_quality_metrics(orders_df, "raw_orders")

        # list of columns that we want string cleaning op to be performed on.
        str_cols = list(
            set(orders_df.select_dtypes("object").columns.to_list())
            - set(["Quantity", "InvoiceNo", "Orderno", "LedgerDate"])
        )

        # Log transformation parameters
        mlflow.log_param("string_columns_to_clean", len(str_cols))
        mlflow.log_param("type_conversion_columns", ["Quantity", "InvoiceNo", "Orderno"])
        mlflow.log_param("datetime_column", "LedgerDate")

        orders_df_clean = (
            orders_df
            # set dtypes
            .change_type(["Quantity", "InvoiceNo", "Orderno"], np.int64)
            # set dtypes
            .to_datetime("LedgerDate", format="%d/%m/%Y")
            # clean string columns (NOTE: only handling datetime columns)
            .transform_columns(str_cols, string_cleaning, elementwise=False)
            # clean column names
            .clean_names(case_type="snake").rename_columns({"orderno": "order_no"})
        )

        # Log cleaning metrics
        cleaning_metrics = {
            "columns_after_cleaning": len(orders_df_clean.columns),
            "rows_after_cleaning": len(orders_df_clean),
            "string_columns_processed": len(str_cols)
        }
        mlflow.log_metrics(cleaning_metrics)

        # Log output dataset info
        log_dataset_info(orders_df_clean, "cleaned_orders")
        log_data_quality_metrics(orders_df_clean, "cleaned_orders")

        # Log processing time
        end_time = datetime.now()
        log_processing_time(start_time, end_time, "orders_cleaning")

        # save dataset
        save_dataset(context, orders_df_clean, output_dataset)
        return orders_df_clean


@register_processor("data-cleaning", "sales")
def clean_sales_table(context, params):
    """Clean the ``SALES`` data table.

    The table is a summary table obtained by doing a ``inner`` join of the
    ``PRODUCT`` and ``ORDERS`` tables.
    """

    input_product_ds = "cleaned/product"
    input_orders_ds = "cleaned/orders"
    output_dataset = "cleaned/sales"

    # Initialize MLflow for data cleaning
    experiment_name = get_experiment_name("data-cleaning")
    init_mlflow(experiment_name)

    # Start MLflow run with tags
    with start_mlflow_run_with_tags(
        run_name="clean_sales_table",
        job_name="data-cleaning",
        stage_name="sales",
        dataset_type="sales"
    ):
        start_time = datetime.now()

        # Log parameters
        mlflow.log_param("input_product_dataset", input_product_ds)
        mlflow.log_param("input_orders_dataset", input_orders_ds)
        mlflow.log_param("output_dataset", output_dataset)
        mlflow.log_param("join_type", "inner")
        mlflow.log_param("join_key", "sku")

        # load datasets
        product_df = load_dataset(context, input_product_ds)
        orders_df = load_dataset(context, input_orders_ds)

        # Log input dataset info
        log_dataset_info(product_df, "input_product")
        log_dataset_info(orders_df, "input_orders")
        log_data_quality_metrics(product_df, "input_product")
        log_data_quality_metrics(orders_df, "input_orders")

        # Perform merge
        sales_df_clean = orders_df.merge(product_df, how="inner", on="sku")

        # Log merge metrics
        merge_metrics = {
            "product_rows": len(product_df),
            "orders_rows": len(orders_df),
            "merged_rows": len(sales_df_clean),
            "merge_efficiency": len(sales_df_clean) / len(orders_df) if len(orders_df) > 0 else 0
        }
        mlflow.log_metrics(merge_metrics)

        # Log output dataset info
        log_dataset_info(sales_df_clean, "cleaned_sales")
        log_data_quality_metrics(sales_df_clean, "cleaned_sales")

        # Log processing time
        end_time = datetime.now()
        log_processing_time(start_time, end_time, "sales_cleaning")

        save_dataset(context, sales_df_clean, output_dataset)
        return sales_df_clean


@register_processor("data-cleaning", "train-test")
def create_training_datasets(context, params):
    """Split the ``HOUSING`` table into ``train`` and ``test`` datasets."""

    input_dataset = "cleaned/housing"
    output_train_features = "train/housing/features"
    output_train_target = "train/housing/target"
    output_test_features = "test/housing/features"
    output_test_target = "test/housing/target"

    # Initialize MLflow for data cleaning
    experiment_name = get_experiment_name("data-cleaning")
    init_mlflow(experiment_name)

    # Start MLflow run with tags
    with start_mlflow_run_with_tags(
        run_name="create_training_datasets",
        job_name="data-cleaning",
        stage_name="train-test",
        dataset_type="train_test_split"
    ):
        start_time = datetime.now()

        # Log parameters
        mlflow.log_param("input_dataset", input_dataset)
        mlflow.log_param("test_size", params["test_size"])
        mlflow.log_param("target_column", params["target"])
        mlflow.log_param("random_seed", context.random_seed)
        mlflow.log_param("split_strategy", "StratifiedShuffleSplit")

        # load dataset
        housing_df_processed = load_dataset(context, input_dataset)

        # Log input dataset info
        log_dataset_info(housing_df_processed, "input_housing")
        log_data_quality_metrics(housing_df_processed, "input_housing")

        # Housing-specific feature engineering (if not already done in cleaning)
        # Create income categories for stratification
        if 'median_income' in housing_df_processed.columns:
            housing_df_processed['income_category'] = pd.cut(
                housing_df_processed['median_income'],
                bins=[0., 1.5, 3.0, 4.5, 6., np.inf],
                labels=[1, 2, 3, 4, 5]
            )

        # Log feature engineering metrics
        feature_metrics = {
            "total_housing_records": len(housing_df_processed),
            "features_available": len(housing_df_processed.columns),
            "income_categories": housing_df_processed['income_category'].nunique() if 'income_category' in housing_df_processed.columns else 0
        }
        mlflow.log_metrics(feature_metrics)

        # split the data
        splitter = StratifiedShuffleSplit(
            n_splits=1, test_size=params["test_size"], random_state=context.random_seed
        )

        # For housing data, use income_category for stratification if available
        if 'income_category' in housing_df_processed.columns:
            housing_df_train, housing_df_test = custom_train_test_split(
                housing_df_processed, splitter, by=lambda df: df['income_category']
            )
        else:
            # Simple random split if no stratification column
            from sklearn.model_selection import train_test_split
            housing_df_train, housing_df_test = train_test_split(
                housing_df_processed,
                test_size=params["test_size"],
                random_state=context.random_seed
            )

        # Log split metrics
        split_metrics = {
            "total_rows": len(housing_df_processed),
            "train_rows": len(housing_df_train),
            "test_rows": len(housing_df_test),
            "train_percentage": len(housing_df_train) / len(housing_df_processed) * 100,
            "test_percentage": len(housing_df_test) / len(housing_df_processed) * 100
        }
        mlflow.log_metrics(split_metrics)

        # split train dataset into features and target
        target_col = params["target"]
        train_X, train_y = (
            housing_df_train
            # split the dataset to train and test
            .get_features_targets(target_column_names=target_col)
        )

        # split test dataset into features and target
        test_X, test_y = (
            housing_df_test
            # split the dataset to train and test
            .get_features_targets(target_column_names=target_col)
        )

        # Log dataset info for all splits
        log_dataset_info(train_X, "train_features")
        log_dataset_info(train_y, "train_target")
        log_dataset_info(test_X, "test_features")
        log_dataset_info(test_y, "test_target")

        log_data_quality_metrics(train_X, "train_features")
        log_data_quality_metrics(test_X, "test_features")

        # save the datasets
        save_dataset(context, train_X, output_train_features)
        save_dataset(context, train_y, output_train_target)
        save_dataset(context, test_X, output_test_features)
        save_dataset(context, test_y, output_test_target)

        # Log processing time
        end_time = datetime.now()
        log_processing_time(start_time, end_time, "train_test_split")

        return {
            "train_shape": train_X.shape,
            "test_shape": test_X.shape,
            "target_column": target_col
        }
