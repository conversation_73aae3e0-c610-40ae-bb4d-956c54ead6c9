"""Pipeline exceptions for HPP_LIB."""


class PipelineError(Exception):
    """Base exception for pipeline-related errors."""
    pass


class TaskError(PipelineError):
    """Exception raised when a task fails."""
    pass


class JobError(PipelineError):
    """Exception raised when a job fails."""
    pass


class ProcessorError(PipelineError):
    """Exception raised when a processor fails."""
    pass
