"""Centralized MLflow configuration for production workflows."""
import os.path as op
from ta_lib.core.api import DEFAULT_ARTIFACTS_PATH

# MLflow Configuration
MLFLOW_CONFIG = {
    "experiment_name": "housing-price-prediction",
    "artifact_location": op.abspath(op.join(DEFAULT_ARTIFACTS_PATH, "mlruns")),
    "tracking_uri": None,  # Use default (local file store)
    "ui_port": 5000,
    "ui_host": "localhost"
}

# Job-specific experiment configurations
JOB_EXPERIMENTS = {
    "data-cleaning": {
        "experiment_name": "housing-price-prediction-data-cleaning",
        "description": "Data cleaning and preprocessing experiments"
    },
    "feat-engg": {
        "experiment_name": "housing-price-prediction-feature-engineering", 
        "description": "Feature engineering experiments"
    },
    "model-gen": {
        "experiment_name": "housing-price-prediction-training",
        "description": "Model training experiments"
    },
    "model-eval": {
        "experiment_name": "housing-price-prediction-evaluation",
        "description": "Model evaluation experiments"
    }
}

# Default tags for all runs
DEFAULT_TAGS = {
    "project": "housing-price-prediction",
    "environment": "production",
    "framework": "ta-lib"
}

def get_experiment_name(job_name=None):
    """Get experiment name for a specific job or default."""
    if job_name and job_name in JOB_EXPERIMENTS:
        return JOB_EXPERIMENTS[job_name]["experiment_name"]
    return MLFLOW_CONFIG["experiment_name"]

def get_mlflow_ui_url():
    """Get the MLflow UI URL."""
    host = MLFLOW_CONFIG["ui_host"]
    port = MLFLOW_CONFIG["ui_port"]
    return f"http://{host}:{port}"

def get_artifact_location():
    """Get the artifact location path."""
    return MLFLOW_CONFIG["artifact_location"]
