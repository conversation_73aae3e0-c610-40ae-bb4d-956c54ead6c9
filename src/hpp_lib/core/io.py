"""IO utilities for HPP_LIB."""

import fsspec
from .utils import get_fs_and_abs_path


def fs(context, uri, credential_id=None):
    """Get filesystem for the given URI."""
    if credential_id is not None:
        credentials = context.credentials.get(credential_id, {})
        # Apply credentials if needed
        fs, _ = get_fs_and_abs_path(uri)
        # TODO: Apply credentials to filesystem
        return fs
    else:
        fs, _ = get_fs_and_abs_path(uri)
        return fs


def get_filesystem(uri, **kwargs):
    """Get filesystem for the given URI."""
    return fsspec.filesystem(uri.split("://")[0] if "://" in uri else "file", **kwargs)
