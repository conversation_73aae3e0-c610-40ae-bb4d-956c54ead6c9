[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "mle-training"
version = "0.1.0"
description = "Machine Learning Exercise Training Project"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "flask>=3.1.1",
    "gunicorn>=23.0.0",
    "matplotlib>=3.10.3",
    "mlflow>=2.22.0",
    "numpy>=2.2.5",
    "pandas>=2.2.3",
    "pytest>=8.3.5",
    "pytest-cov>=6.1.1",
    "ruff>=0.12.0",
    "scikit-learn>=1.6.1",
    "seaborn>=0.13.2",
    "six>=1.17.0",
]

[dependency-groups]
dev = [
    "black>=25.1.0",
    "flake8>=7.2.0",
    "isort>=6.0.1",
]

[tool.setuptools]
packages = [
    "housing_value_predictor",
    "housing_value_predictor.config",
    "housing_value_predictor.utils"
]
package-dir = {"" = "src"}
