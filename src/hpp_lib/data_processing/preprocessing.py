"""Data preprocessing utilities for HPP_LIB."""

import pandas as pd
import numpy as np
from sklearn.base import BaseEstimator, TransformerMixin
from .outliers import Outlier


class DataProcessor(BaseEstimator, TransformerMixin):
    """General data preprocessing transformer for HPP_LIB."""
    
    def __init__(self, 
                 handle_outliers=True,
                 outlier_method="percentile",
                 outlier_drop=False,
                 handle_missing=True,
                 missing_strategy="median"):
        """Initialize DataProcessor.
        
        Parameters
        ----------
        handle_outliers : bool, default True
            Whether to handle outliers
        outlier_method : str, default "percentile"
            Method for outlier detection
        outlier_drop : bool, default False
            Whether to drop outliers or cap them
        handle_missing : bool, default True
            Whether to handle missing values
        missing_strategy : str, default "median"
            Strategy for handling missing values
        """
        self.handle_outliers = handle_outliers
        self.outlier_method = outlier_method
        self.outlier_drop = outlier_drop
        self.handle_missing = handle_missing
        self.missing_strategy = missing_strategy
        
        self.outlier_transformer = None
        self.missing_values = {}
        
    def fit(self, X, y=None):
        """Fit the preprocessor to the data."""
        if self.handle_outliers:
            self.outlier_transformer = Outlier(method=self.outlier_method)
            self.outlier_transformer.fit(X)
            
        if self.handle_missing:
            # Compute missing value replacements
            numeric_cols = X.select_dtypes(include=[np.number]).columns
            categorical_cols = X.select_dtypes(include=['object']).columns
            
            for col in numeric_cols:
                if self.missing_strategy == "median":
                    self.missing_values[col] = X[col].median()
                elif self.missing_strategy == "mean":
                    self.missing_values[col] = X[col].mean()
                else:
                    self.missing_values[col] = 0
                    
            for col in categorical_cols:
                self.missing_values[col] = X[col].mode().iloc[0] if len(X[col].mode()) > 0 else "unknown"
                
        return self
        
    def transform(self, X):
        """Transform the data."""
        X_transformed = X.copy()
        
        if self.handle_outliers and self.outlier_transformer is not None:
            X_transformed = self.outlier_transformer.transform(X_transformed, drop=self.outlier_drop)
            
        if self.handle_missing:
            for col, fill_value in self.missing_values.items():
                if col in X_transformed.columns:
                    X_transformed[col] = X_transformed[col].fillna(fill_value)
                    
        return X_transformed
