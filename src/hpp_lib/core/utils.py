"""Utility functions for HPP_LIB core module."""

import os
import os.path as op
import random
import importlib.util
import yaml
import fsspec
import numpy as np
from .. import version


def get_package_version():
    """Get the package version."""
    return version.__version__


def initialize_random_seed(seed=None):
    """Initialize random seed for reproducibility."""
    if seed is None:
        seed = 42
    
    random.seed(seed)
    np.random.seed(seed)
    
    # Set environment variable for other libraries
    os.environ['PYTHONHASHSEED'] = str(seed)
    
    return seed


def import_python_file(file_path):
    """Import a Python file as a module."""
    spec = importlib.util.spec_from_file_location("module", file_path)
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module


def load_yml(file_path, fs=None):
    """Load YAML file."""
    if fs is None:
        fs = fsspec.filesystem("file")
    
    with fs.open(file_path, 'r') as f:
        return yaml.safe_load(f)


def save_yml(data, file_path, fs=None):
    """Save data to YAML file."""
    if fs is None:
        fs = fsspec.filesystem("file")
    
    # Ensure directory exists
    fs.makedirs(op.dirname(file_path), exist_ok=True)
    
    with fs.open(file_path, 'w') as f:
        yaml.safe_dump(data, f, default_flow_style=False)


def get_fs_and_abs_path(path):
    """Get filesystem and absolute path."""
    if "://" in path:
        # Remote path
        fs = fsspec.filesystem(path.split("://")[0])
        abs_path = path
    else:
        # Local path
        fs = fsspec.filesystem("file")
        abs_path = op.abspath(path)
    
    return fs, abs_path


def ensure_dir_exists(path):
    """Ensure directory exists."""
    os.makedirs(path, exist_ok=True)


def get_file_extension(path):
    """Get file extension from path."""
    return op.splitext(path)[1].lower()


def is_file_supported(path, supported_formats):
    """Check if file format is supported."""
    ext = get_file_extension(path)
    return ext in supported_formats
