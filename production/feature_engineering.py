"""Processors for the feature engineering step of the worklow.

The step loads cleaned training data, processes the data for outliers,
missing values and any other cleaning steps based on business rules/intuition.

The trained pipeline and any artifacts are then saved to be used in
training/scoring pipelines.
"""
import logging
import os.path as op
import mlflow
from datetime import datetime

from category_encoders import TargetEncoder
from sklearn.compose import ColumnTransformer
from sklearn.impute import SimpleImputer
from sklearn.pipeline import Pipeline

from ta_lib.core.api import (
    get_dataframe,
    get_feature_names_from_column_transformer,
    get_package_path,
    load_dataset,
    register_processor,
    save_pipeline,
    DEFAULT_ARTIFACTS_PATH
)

from ta_lib.data_processing.api import Outlier
from production.mlflow_integration import (
    init_mlflow,
    log_data_quality_metrics,
    log_dataset_info,
    start_mlflow_run_with_tags,
    log_processing_time,
    log_model_artifact
)
from production.mlflow_config import get_experiment_name

logger = logging.getLogger(__name__)


@register_processor("feat-engg", "transform-features")
def transform_features(context, params):
    """Transform dataset to create training datasets."""
    from datetime import datetime

    input_features_ds = "train/sales/features"
    input_target_ds = "train/sales/target"

    artifacts_folder = DEFAULT_ARTIFACTS_PATH

    # Initialize MLflow for feature engineering
    experiment_name = get_experiment_name("feat-engg")
    init_mlflow(experiment_name)

    # Start MLflow run with tags
    with start_mlflow_run_with_tags(
        run_name="transform_features",
        job_name="feat-engg",
        stage_name="transform-features",
        processing_type="feature_engineering"
    ):
        start_time = datetime.now()

        # Log parameters
        mlflow.log_param("input_features_dataset", input_features_ds)
        mlflow.log_param("input_target_dataset", input_target_ds)
        mlflow.log_param("outlier_method", params["outliers"]["method"])
        mlflow.log_param("outlier_drop", params["outliers"]["drop"])
        mlflow.log_param("sampling_fraction", params.get("sampling_fraction", "None"))

        # load datasets
        train_X = load_dataset(context, input_features_ds)
        train_y = load_dataset(context, input_target_ds)

        # Log input dataset info
        log_dataset_info(train_X, "input_features")
        log_dataset_info(train_y, "input_target")
        log_data_quality_metrics(train_X, "input_features")

        cat_columns = train_X.select_dtypes("object").columns
        num_columns = train_X.select_dtypes("number").columns

        # Log column type information
        column_info = {
            "total_columns": len(train_X.columns),
            "categorical_columns": len(cat_columns),
            "numerical_columns": len(num_columns)
        }
        mlflow.log_metrics(column_info)
        mlflow.log_param("categorical_columns", list(cat_columns))
        mlflow.log_param("numerical_columns", list(num_columns))

        # Treating Outliers
        outlier_transformer = Outlier(method=params["outliers"]["method"])
        train_X_before_outliers = train_X.copy()
        train_X = outlier_transformer.fit_transform(
            train_X, drop=params["outliers"]["drop"]
        )

        # Log outlier treatment metrics
        outlier_metrics = {
            "rows_before_outlier_treatment": len(train_X_before_outliers),
            "rows_after_outlier_treatment": len(train_X),
            "rows_removed_by_outlier_treatment": len(train_X_before_outliers) - len(train_X)
        }
        mlflow.log_metrics(outlier_metrics)

        # NOTE: You can use ``Pipeline`` to compose a collection of transformers
        # into a single transformer. In this case, we are composing a
        # ``TargetEncoder`` and a ``SimpleImputer`` to first encode the
        # categorical variable into a numerical values and then impute any missing
        # values using ``most_frequent`` strategy.
        tgt_enc_simple_impt = Pipeline(
            [
                ("target_encoding", TargetEncoder(return_df=False)),
                ("simple_impute", SimpleImputer(strategy="most_frequent")),
            ]
        )

        # NOTE: the list of transformations here are not sequential but weighted
        # (if multiple transforms are specified for a particular column)
        # for sequential transforms use a pipeline as shown above.
        special_cat_cols = ["technology", "functional_status", "platforms"]
        regular_cat_cols = list(set(cat_columns) - set(special_cat_cols))

        features_transformer = ColumnTransformer(
            [
                # categorical columns
                ("tgt_enc", TargetEncoder(return_df=False), regular_cat_cols),
                ("tgt_enc_sim_impt", tgt_enc_simple_impt, special_cat_cols),
                # numeric columns
                ("med_enc", SimpleImputer(strategy="median"), num_columns),
            ]
        )

        # Log transformer configuration
        mlflow.log_param("regular_categorical_columns", regular_cat_cols)
        mlflow.log_param("special_categorical_columns", special_cat_cols)
        mlflow.log_param("imputation_strategy_numeric", "median")
        mlflow.log_param("imputation_strategy_categorical", "most_frequent")

        # Check if the data should be sampled. This could be useful to quickly run
        # the pipeline for testing/debugging purposes (undersample)
        # or profiling purposes (oversample).
        # The below is an example how the sampling can be done on the train data if required.
        # Model Training in this reference code has been done on complete train data itself.
        sample_frac = params.get("sampling_fraction", None)
        if sample_frac is not None:
            logger.warning(f"The data has been sample by fraction: {sample_frac}")
            sample_X = train_X.sample(frac=sample_frac, random_state=context.random_seed)
            mlflow.log_metric("sampling_applied", 1)
            mlflow.log_metric("sampled_rows", len(sample_X))
        else:
            sample_X = train_X
            mlflow.log_metric("sampling_applied", 0)
        sample_y = train_y.loc[sample_X.index]

        # Train the feature engg. pipeline prepared earlier. Note that the pipeline is
        # fitted on only the **training data** and not the full dataset.
        # This avoids leaking information about the test dataset when training the model.
        # In the below code train_X, train_y in the fit_transform can be replaced with
        # sample_X and sample_y if required.
        train_X_transformed = get_dataframe(
            features_transformer.fit_transform(train_X, train_y),
            get_feature_names_from_column_transformer(features_transformer),
        )

        # Note: we can create a transformer/feature selector that simply drops
        # a specified set of columns. But, we don't do that here to illustrate
        # what to do when transformations don't cleanly fall into the sklearn
        # pattern.
        columns_to_drop = [
            "manufacturer", "inventory_id", "ext_grade", "source_channel",
            "tgt_enc_iter_impt_platforms", "ext_model_family", "order_no", "line",
            "inventory_id", "gp", "selling_price", "selling_cost", "invoice_no", "customername"
        ]

        curated_columns = list(set(train_X_transformed.columns.to_list()) - set(columns_to_drop))

        # Log feature selection metrics
        feature_selection_metrics = {
            "features_before_curation": len(train_X_transformed.columns),
            "features_after_curation": len(curated_columns),
            "features_dropped": len(train_X_transformed.columns) - len(curated_columns)
        }
        mlflow.log_metrics(feature_selection_metrics)
        mlflow.log_param("dropped_columns", columns_to_drop)
        mlflow.log_param("final_feature_columns", curated_columns)

        # Log final transformed dataset info
        log_dataset_info(train_X_transformed, "transformed_features")
        log_data_quality_metrics(train_X_transformed, "transformed_features")

        # saving the list of relevant columns and the pipeline.
        curated_columns_path = op.abspath(op.join(artifacts_folder, "curated_columns.joblib"))
        features_transformer_path = op.abspath(op.join(artifacts_folder, "features.joblib"))

        save_pipeline(curated_columns, curated_columns_path)
        save_pipeline(features_transformer, features_transformer_path)

        # Log artifacts to MLflow
        log_model_artifact(curated_columns_path, "feature_engineering")
        log_model_artifact(features_transformer_path, "feature_engineering")

        # Log processing time
        end_time = datetime.now()
        log_processing_time(start_time, end_time, "feature_engineering")

        return {
            "curated_columns_count": len(curated_columns),
            "transformer_path": features_transformer_path,
            "curated_columns_path": curated_columns_path
        }