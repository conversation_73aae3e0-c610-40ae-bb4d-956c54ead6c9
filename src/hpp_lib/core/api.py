"""Core utilities common to all usecases for HPP_LIB.

This is a namespace housing all the core utilities that could be useful to
an end user. This includes IO utilities, Job management utilities and utilities
to manage project configuration.
"""

# Core context and configuration
from .context import create_context, Context

# Dataset management
from .dataset import load_dataset, save_dataset, list_datasets

# Pipeline management
from .pipelines.processors import register_processor, list_jobs, load_job_processors, get_job_processors
from .pipelines.job_planner import create_job_plan
from .pipelines.job_runner import main as job_runner
from .pipelines import job_planner, job_runner

# Tracking and utilities
from .tracking import (
    DEFAULT_ARTIFACTS_PATH,
    load_pipeline,
    save_pipeline,
    get_dataframe,
    get_feature_names_from_column_transformer,
    string_cleaning,
    custom_train_test_split,
    load_data,
    save_data,
    start_experiment,
    is_tracker_supported,
    create_client
)

# IO utilities
from .io import fs, get_filesystem

# Utility functions
from .utils import (
    get_package_version,
    initialize_random_seed,
    import_python_file,
    load_yml,
    save_yml,
    get_fs_and_abs_path,
    ensure_dir_exists
)

# Constants
from .constants import *
