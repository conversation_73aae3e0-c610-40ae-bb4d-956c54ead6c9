"""Processors for the model training step of the worklow."""
import logging
import os.path as op
import mlflow

from sklearn.pipeline import Pipeline
from sklearn.metrics import mean_squared_error, r2_score

from ta_lib.core.api import (
    get_dataframe,
    get_feature_names_from_column_transformer,
    get_package_path,
    load_dataset,
    load_pipeline,
    register_processor,
    save_pipeline,
    DEFAULT_ARTIFACTS_PATH
)
from ta_lib.regression.api import SKLStatsmodelOLS
from production.mlflow_integration import (
    init_mlflow,
    log_model_metrics,
    log_model_params,
    log_sklearn_model,
    log_dataset_info,
    log_data_quality_metrics,
    start_mlflow_run_with_tags,
    log_processing_time,
    log_model_artifact
)
from production.mlflow_config import get_experiment_name

logger = logging.getLogger(__name__)


@register_processor("model-gen", "train-model")
def train_model(context, params):
    """Train a regression model."""
    from datetime import datetime

    artifacts_folder = DEFAULT_ARTIFACTS_PATH

    input_features_ds = "train/sales/features"
    input_target_ds = "train/sales/target"

    # Initialize MLflow
    experiment_name = get_experiment_name("model-gen")
    init_mlflow(experiment_name)

    # Start MLflow run with tags
    with start_mlflow_run_with_tags(
        run_name="train_model",
        job_name="model-gen",
        stage_name="train-model",
        model_type="regression"
    ):
        start_time = datetime.now()

        # Log parameters
        log_model_params(params)
        mlflow.log_param("input_features_dataset", input_features_ds)
        mlflow.log_param("input_target_dataset", input_target_ds)
        mlflow.log_param("model_algorithm", "SKLStatsmodelOLS")
        mlflow.log_param("random_seed", context.random_seed)

        # load training datasets
        train_X = load_dataset(context, input_features_ds)
        train_y = load_dataset(context, input_target_ds)

        # Log input dataset info
        log_dataset_info(train_X, "train_features")
        log_dataset_info(train_y, "train_target")
        log_data_quality_metrics(train_X, "train_features")

        # load pre-trained feature pipelines and other artifacts
        curated_columns_path = op.join(artifacts_folder, "curated_columns.joblib")
        features_transformer_path = op.join(artifacts_folder, "features.joblib")

        curated_columns = load_pipeline(curated_columns_path)
        features_transformer = load_pipeline(features_transformer_path)

        # Log pipeline info
        mlflow.log_param("curated_columns_count", len(curated_columns))
        mlflow.log_param("feature_transformer_loaded", True)

        # transform the training dataset
        train_X_transformed = get_dataframe(
            features_transformer.transform(train_X),
            get_feature_names_from_column_transformer(features_transformer),
        )
        train_X_final = train_X_transformed[curated_columns]

        # Log transformation metrics
        transformation_metrics = {
            "features_before_transformation": len(train_X.columns),
            "features_after_transformation": len(train_X_transformed.columns),
            "features_after_curation": len(train_X_final.columns),
            "training_samples": len(train_X_final)
        }
        mlflow.log_metrics(transformation_metrics)

        # Log final dataset info
        log_dataset_info(train_X_final, "final_train_features")
        log_data_quality_metrics(train_X_final, "final_train_features")

        # create training pipeline
        reg_ppln_ols = Pipeline([("estimator", SKLStatsmodelOLS())])

        # fit the training pipeline
        reg_ppln_ols.fit(train_X_final, train_y.values.ravel())

        # Calculate and log metrics
        train_predictions = reg_ppln_ols.predict(train_X_final)
        mse = mean_squared_error(train_y, train_predictions)
        r2 = r2_score(train_y, train_predictions)
        rmse = mse ** 0.5

        # Additional metrics
        import numpy as np
        mae = np.mean(np.abs(train_y.values.ravel() - train_predictions))

        metrics = {
            "training_mse": mse,
            "training_rmse": rmse,
            "training_mae": mae,
            "training_r2": r2
        }
        log_model_metrics(metrics)

        # Log model to MLflow
        log_sklearn_model(reg_ppln_ols, "regression_model")

        # save fitted training pipeline
        model_path = op.abspath(op.join(artifacts_folder, "train_pipeline.joblib"))
        save_pipeline(reg_ppln_ols, model_path)

        # Log the saved model as an artifact
        log_model_artifact(model_path, "model_artifacts")

        # Log processing time
        end_time = datetime.now()
        log_processing_time(start_time, end_time, "model_training")

        return {"model_path": model_path, "metrics": metrics}
