"""Regression metrics for HPP_LIB."""

import numpy as np
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error


class RegressionMetrics:
    """Utility class for computing regression metrics."""
    
    @staticmethod
    def mse(y_true, y_pred):
        """Mean Squared Error."""
        return mean_squared_error(y_true, y_pred)
    
    @staticmethod
    def rmse(y_true, y_pred):
        """Root Mean Squared Error."""
        return np.sqrt(mean_squared_error(y_true, y_pred))
    
    @staticmethod
    def mae(y_true, y_pred):
        """Mean Absolute Error."""
        return mean_absolute_error(y_true, y_pred)
    
    @staticmethod
    def r2(y_true, y_pred):
        """R-squared Score."""
        return r2_score(y_true, y_pred)
    
    @staticmethod
    def mape(y_true, y_pred):
        """Mean Absolute Percentage Error."""
        return np.mean(np.abs((y_true - y_pred) / y_true)) * 100
    
    @staticmethod
    def compute_all_metrics(y_true, y_pred):
        """Compute all regression metrics."""
        return {
            'mse': RegressionMetrics.mse(y_true, y_pred),
            'rmse': RegressionMetrics.rmse(y_true, y_pred),
            'mae': RegressionMetrics.mae(y_true, y_pred),
            'r2': RegressionMetrics.r2(y_true, y_pred),
            'mape': RegressionMetrics.mape(y_true, y_pred)
        }
