"""Processors for the model training step of the worklow."""
import logging
import os.path as op
import mlflow

from sklearn.pipeline import Pipeline
from sklearn.metrics import mean_squared_error, r2_score

from ta_lib.core.api import (
    get_dataframe,
    get_feature_names_from_column_transformer,
    get_package_path,
    load_dataset,
    load_pipeline,
    register_processor,
    save_pipeline,
    DEFAULT_ARTIFACTS_PATH
)
from ta_lib.regression.api import SKLStatsmodelOLS
from production.mlflow_integration import init_mlflow, log_model_metrics, log_model_params, log_sklearn_model

logger = logging.getLogger(__name__)


@register_processor("model-gen", "train-model")
def train_model(context, params):
    """Train a regression model."""
    artifacts_folder = DEFAULT_ARTIFACTS_PATH

    input_features_ds = "train/sales/features"
    input_target_ds = "train/sales/target"
    
    # Initialize MLflow
    experiment_name = "housing-price-prediction"
    artifact_location = op.abspath(op.join(artifacts_folder, "mlruns"))
    client, experiment_id = init_mlflow(experiment_name, artifact_location)
    
    # Start MLflow run
    with mlflow.start_run(run_name="train_model"):
        # Log parameters
        log_model_params(params)
        
        # load training datasets
        train_X = load_dataset(context, input_features_ds)
        train_y = load_dataset(context, input_target_ds)

        # load pre-trained feature pipelines and other artifacts
        curated_columns = load_pipeline(op.join(artifacts_folder, "curated_columns.joblib"))
        features_transformer = load_pipeline(op.join(artifacts_folder, "features.joblib"))
        
        # transform the training dataset
        train_X = get_dataframe(
            features_transformer.transform(train_X),
            get_feature_names_from_column_transformer(features_transformer),
        )
        train_X = train_X[curated_columns]

        # create training pipeline
        reg_ppln_ols = Pipeline([("estimator", SKLStatsmodelOLS())])

        # fit the training pipeline
        reg_ppln_ols.fit(train_X, train_y.values.ravel())
        
        # Calculate and log metrics
        train_predictions = reg_ppln_ols.predict(train_X)
        mse = mean_squared_error(train_y, train_predictions)
        r2 = r2_score(train_y, train_predictions)
        
        metrics = {
            "training_mse": mse,
            "training_r2": r2
        }
        log_model_metrics(metrics)
        
        # Log model to MLflow
        log_sklearn_model(reg_ppln_ols, "regression_model")

        # save fitted training pipeline
        model_path = op.abspath(op.join(artifacts_folder, "train_pipeline.joblib"))
        save_pipeline(reg_ppln_ols, model_path)
        
        # Log the saved model as an artifact
        mlflow.log_artifact(model_path, "model_artifacts")
        
        return {"model_path": model_path, "metrics": metrics}
