from pprint import pprint
import os
import os.path as op
import shutil

# standard third party imports
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
pd.options.mode.use_inf_as_na = True


%load_ext autoreload
%autoreload 2

import warnings
from numba import NumbaDeprecationWarning

warnings.filterwarnings('ignore', message="The default value of regex will change from True to False in a future version.", 
                        category=FutureWarning)

warnings.filterwarnings("ignore", category=NumbaDeprecationWarning)

# standard code-template imports
from ta_lib.core.api import (
    create_context, get_dataframe, get_feature_names_from_column_transformer, get_package_path,
    display_as_tabs, string_cleaning, merge_info, initialize_environment,
    list_datasets, load_dataset, save_dataset
)
import ta_lib.eda.api as eda

initialize_environment(debug=False, hide_warnings=True)

config_path = op.join('conf', 'config.yml')
context = create_context(config_path)
pprint(list_datasets(context))

orders_df = load_dataset(context, 'raw/orders')
prod_df = load_dataset(context, 'raw/product')

prod_df_clean = (
    prod_df
    # while iterating on testing, it's good to copy the dataset(or a subset)
    # as the following steps will mutate the input dataframe. The copy should be
    # removed in the production code to avoid introducing perf. bottlenecks.
    .copy()

    # set dtypes : nothing to do here
    .passthrough()

    .transform_columns(prod_df.columns.to_list(), string_cleaning, elementwise=False)
    
    .replace({'': np.nan})
    
    # drop unnecessary cols : nothing to do here
    .coalesce(['color', 'Ext_Color'], 'color', delete_columns=True)
    
    # drop unnecessary cols : nothing to do here
    .coalesce(['MemorySize', 'Ext_memorySize'], 'memory_size', delete_columns=True)
    
    # ensure that the key column does not have duplicate records
    .remove_duplicate_rows(col_names=['SKU'], keep_first=True)
    
    # clean column names (comment out this line while cleaning data above)
    .clean_names(case_type='snake')
)
prod_df_clean.head()

save_dataset(context, prod_df_clean, 'cleaned/product')

# column names after cleaning

str_cols = list(
    set(orders_df.select_dtypes('object').columns.to_list()) 
    - set(['Customername', 'InvoiceNo','Quantity', 'InvoiceNo', 'Orderno', 'LedgerDate'])
)
orders_df_clean = (
    orders_df
    
    .copy()
    #.sample(frac=1, resample=False)

    # set dtypes
    .change_type(['Quantity', 'InvoiceNo', 'Orderno'], np.int64)
    
    # set dtypes
    .to_datetime('LedgerDate', format='%d/%m/%Y')
    
    # clean string columns (NOTE: only after setting datetime)
    .transform_columns(str_cols, string_cleaning, elementwise=False)

    # clean column names                                                                                                                                   
    .clean_names(case_type='snake')
    .rename_columns({'orderno': 'order_no'})
)
orders_df_clean.head().T


save_dataset(context, orders_df_clean, 'cleaned/orders')

sales_df = pd.merge(orders_df_clean, prod_df_clean, how='inner', on='sku', validate='m:1')
merge_info(orders_df_clean, prod_df_clean, sales_df)

# first time customer
cust_details = sales_df.groupby(['customername']).agg({'ledger_date':'min'}).reset_index()
cust_details.columns = ['customername','ledger_date']
cust_details['first_time_customer'] = 1
sales_df = sales_df.merge(cust_details, on=['customername','ledger_date'], how='left')
sales_df['first_time_customer'].fillna(0, inplace=True)

#### days since last purchase
sales_df.sort_values('ledger_date',inplace=True)
sales_df['days_since_last_purchase'] = (
    sales_df
       .groupby('customername')['ledger_date']
       .diff()
       .dt.days
       .fillna(0, downcast='infer'))

# create a sample dataframe with minimal processing

sales_df_processed = (
    sales_df
    
    # tweak to test pipeline quickly or profile performance
    #.sample(frac=1, replace=False)
    
    # any additional processing/cleaning
)

# Any verifications on the data
from ta_lib.eda.api import get_variable_summary
display_as_tabs([
    ("Summary", f"Length: {len(sales_df_processed)}, Columns: {len(sales_df_processed.columns)}"),
    ("Variable summary", get_variable_summary(sales_df_processed)),
    ("head", sales_df.head(5).T),
    ("tail", sales_df.tail(5).T),
])

save_dataset(context, sales_df_processed, 'cleaned/sales')

from sklearn.model_selection import StratifiedShuffleSplit
from ta_lib.core.api import custom_train_test_split  # helper function to customize splitting
from scripts import *

splitter = StratifiedShuffleSplit(n_splits=1, test_size=0.2, random_state=context.random_seed)
sales_df_train, sales_df_test = custom_train_test_split(sales_df_processed, splitter, by=binned_selling_price)

target_col = "unit_price"

train_X, train_y = (
    sales_df_train
    
    # split the dataset to train and test
    .get_features_targets(target_column_names=target_col)
)
save_dataset(context, train_X, 'train/sales/features')
save_dataset(context, train_y, 'train/sales/target')


test_X, test_y = (
    sales_df_test
    
    # split the dataset to train and test
    .get_features_targets(target_column_names=target_col)
)
save_dataset(context, test_X, 'test/sales/features')
save_dataset(context, test_y, 'test/sales/target')

