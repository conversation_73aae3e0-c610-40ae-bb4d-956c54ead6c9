"""
Housing Value Predictor package.

This package contains modules for data ingestion, model training, and scoring
for predicting housing values based on various features.
"""

from housing_value_predictor import ingest_data, score, train
from housing_value_predictor.config.config import *
from housing_value_predictor.utils.logging_utils import setup_logging

__all__ = [
    'setup_logging',
    'ingest_data',
    'train',
    'score',
]
