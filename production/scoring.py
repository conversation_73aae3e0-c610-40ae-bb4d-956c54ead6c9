"""Processors for the model scoring/evaluation step of the worklow."""
import os.path as op
import mlflow
from sklearn.metrics import mean_squared_error, r2_score

from ta_lib.core.api import (
    get_dataframe,
    get_feature_names_from_column_transformer,
    load_dataset,
    load_pipeline, 
    register_processor, 
    save_dataset, 
    DEFAULT_ARTIFACTS_PATH
)
from production.mlflow_integration import (
    init_mlflow,
    log_model_metrics,
    log_model_artifact,
    get_mlflow_run_url,
    log_dataset_info,
    log_data_quality_metrics,
    start_mlflow_run_with_tags,
    log_processing_time
)
from production.mlflow_config import get_experiment_name


@register_processor("model-eval", "score-model")
def score_model(context, params):
    """Score a pre-trained model."""
    from datetime import datetime

    input_features_ds = "test/housing/features"
    input_target_ds = "test/housing/target"
    output_ds = "score/housing/output"

    artifacts_folder = DEFAULT_ARTIFACTS_PATH
    print(f"Using artifacts folder: {artifacts_folder}")

    # Initialize MLflow
    experiment_name = get_experiment_name("model-eval")
    init_mlflow(experiment_name)

    # Start MLflow run with tags
    with start_mlflow_run_with_tags(
        run_name="score_model",
        job_name="model-eval",
        stage_name="score-model",
        evaluation_type="test_set_evaluation"
    ) as run:
        start_time = datetime.now()
        run_id = run.info.run_id
        print(f"Started MLflow run with ID: {run_id}")

        # Log parameters
        mlflow.log_param("input_features_dataset", input_features_ds)
        mlflow.log_param("input_target_dataset", input_target_ds)
        mlflow.log_param("output_dataset", output_ds)
        mlflow.log_param("model_type", "regression")

        # load test datasets
        print("Loading test datasets...")
        test_X = load_dataset(context, input_features_ds)
        test_y = load_dataset(context, input_target_ds)
        print(f"Loaded test data: X shape={test_X.shape}, y shape={test_y.shape}")

        # Log input dataset info
        log_dataset_info(test_X, "test_features")
        log_dataset_info(test_y, "test_target")
        log_data_quality_metrics(test_X, "test_features")

        # load the feature pipeline and training pipelines
        print("Loading pipelines...")
        curated_columns_path = op.join(artifacts_folder, "curated_columns.joblib")
        features_transformer_path = op.join(artifacts_folder, "features.joblib")
        model_pipeline_path = op.join(artifacts_folder, "train_pipeline.joblib")

        curated_columns = load_pipeline(curated_columns_path)
        features_transformer = load_pipeline(features_transformer_path)
        model_pipeline = load_pipeline(model_pipeline_path)
        print("Pipelines loaded successfully")

        # Log pipeline info
        mlflow.log_param("curated_columns_count", len(curated_columns))
        mlflow.log_param("pipelines_loaded", True)

        # transform the test dataset
        print("Transforming test dataset...")
        test_X_transformed = get_dataframe(
            features_transformer.transform(test_X),
            get_feature_names_from_column_transformer(features_transformer),
        )
        test_X_final = test_X_transformed[curated_columns]
        print(f"Transformed test data shape: {test_X_final.shape}")

        # Log transformation metrics
        transformation_metrics = {
            "test_features_before_transformation": len(test_X.columns),
            "test_features_after_transformation": len(test_X_transformed.columns),
            "test_features_after_curation": len(test_X_final.columns),
            "test_samples": len(test_X_final)
        }
        mlflow.log_metrics(transformation_metrics)

        # Log final dataset info
        log_dataset_info(test_X_final, "final_test_features")
        log_data_quality_metrics(test_X_final, "final_test_features")

        # make a prediction
        print("Making predictions...")
        predictions = model_pipeline.predict(test_X_final)
        test_X_final["yhat"] = predictions
        print("Predictions completed")

        # Calculate and log metrics
        print("Calculating metrics...")
        mse = mean_squared_error(test_y, predictions)
        r2 = r2_score(test_y, predictions)
        rmse = mse ** 0.5

        # Additional metrics
        import numpy as np
        mae = np.mean(np.abs(test_y.values.ravel() - predictions))

        # Calculate prediction statistics
        pred_stats = {
            "prediction_mean": np.mean(predictions),
            "prediction_std": np.std(predictions),
            "prediction_min": np.min(predictions),
            "prediction_max": np.max(predictions),
            "target_mean": np.mean(test_y.values),
            "target_std": np.std(test_y.values)
        }

        metrics = {
            "test_mse": mse,
            "test_rmse": rmse,
            "test_mae": mae,
            "test_r2": r2,
            **pred_stats
        }
        print(f"Calculated metrics: {metrics}")

        # Log metrics to MLflow
        print("Logging metrics to MLflow...")
        log_model_metrics(metrics)

        # Log the predictions as an artifact
        print("Saving predictions and logging as artifact...")
        predictions_path = op.abspath(op.join(artifacts_folder, "predictions.csv"))
        test_X_final.to_csv(predictions_path, index=False)
        print(f"Saved predictions to {predictions_path}")

        log_model_artifact(predictions_path, "predictions")

        # Create and log a summary report
        summary_path = op.abspath(op.join(artifacts_folder, "evaluation_summary.txt"))
        with open(summary_path, 'w') as f:
            f.write("Model Evaluation Summary\n")
            f.write("=" * 25 + "\n\n")
            f.write(f"Test Set Size: {len(test_X_final)} samples\n")
            f.write(f"Features Used: {len(test_X_final.columns) - 1}\n")  # -1 for yhat column
            f.write("Model Type: Regression\n\n")
            f.write("Performance Metrics:\n")
            f.write(f"- R² Score: {r2:.4f}\n")
            f.write(f"- RMSE: {rmse:.4f}\n")
            f.write(f"- MAE: {mae:.4f}\n")
            f.write(f"- MSE: {mse:.4f}\n\n")
            f.write("Prediction Statistics:\n")
            f.write(f"- Mean Prediction: {pred_stats['prediction_mean']:.4f}\n")
            f.write(f"- Std Prediction: {pred_stats['prediction_std']:.4f}\n")
            f.write(f"- Min Prediction: {pred_stats['prediction_min']:.4f}\n")
            f.write(f"- Max Prediction: {pred_stats['prediction_max']:.4f}\n")

        log_model_artifact(summary_path, "evaluation_reports")

        # store the predictions for any further processing
        save_dataset(context, test_X_final, output_ds)
        print(f"Saved predictions to dataset: {output_ds}")

        # Log processing time
        end_time = datetime.now()
        log_processing_time(start_time, end_time, "model_evaluation")

        # Print the URL to view the run in the MLflow UI
        experiment_id = mlflow.get_experiment_by_name(experiment_name).experiment_id
        run_url = get_mlflow_run_url(experiment_id, run_id)
        print(f"MLflow run completed: {run_id}")
        print(f"View this run at: {run_url}")

        return {"metrics": metrics, "run_id": run_id, "run_url": run_url}
