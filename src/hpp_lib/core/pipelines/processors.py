"""Processor registration and management for HPP_LIB."""

import functools
import glob
import os.path as op
from ..utils import import_python_file

# Private variable to track registered processors
_PROCESSORS = {}


def load_job_processors(folder):
    """Load job processors defined in py files in the provided path."""
    py_files = glob.glob(op.join(folder, "*.py"))
    for path in py_files:
        if path == op.abspath(__file__):
            continue
        import_python_file(path)


def get_job_processors(job_name):
    """Return the processors available for the given job."""
    try:
        return _PROCESSORS[job_name]
    except KeyError:
        avlb_jobs = list_jobs()
        raise ValueError(
            f"Unexpected job name : {job_name}. \n\nMust be one of {avlb_jobs}"
        )


def list_jobs():
    """Return a list of available jobs."""
    return list(_PROCESSORS.keys())


def register_processor(job_name, task_name):
    """Decorator to register a processor function for a specific job and task."""
    def decorator(func):
        if job_name not in _PROCESSORS:
            _PROCESSORS[job_name] = {}
        
        _PROCESSORS[job_name][task_name] = func
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)
        
        return wrapper
    return decorator


def _wrapper(func):
    """Wrapper function for processors."""
    @functools.wraps(func)
    def inner(*args, **kwargs):
        return func(*args, **kwargs)
    return inner
