"""MLflow integration for production workflows."""
import os
import os.path as op
import mlflow
from mlflow.tracking import M<PERSON>low<PERSON><PERSON>

def init_mlflow(experiment_name, artifact_location=None):
    """
    Initialize MLflow tracking.
    
    Parameters:
        experiment_name (str): Name of the MLflow experiment
        artifact_location (str, optional): Location to store artifacts
        
    Returns:
        tuple: (MLflow client, experiment ID)
    """
    print(f"Initializing MLflow with experiment: {experiment_name}")
    print(f"Artifact location: {artifact_location}")
    
    # Get current tracking URI
    tracking_uri = mlflow.get_tracking_uri()
    print(f"Current MLflow tracking URI: {tracking_uri}")
    
    # Create MLflow client
    client = mlflow.tracking.MlflowClient()
    
    # Check if experiment exists
    experiment = mlflow.get_experiment_by_name(experiment_name)
    
    if experiment is None:
        print(f"Creating new experiment: {experiment_name}")
        experiment_id = mlflow.create_experiment(
            name=experiment_name,
            artifact_location=artifact_location
        )
        print(f"Created experiment with ID: {experiment_id}")
    else:
        experiment_id = experiment.experiment_id
        print(f"Using existing experiment with ID: {experiment_id}")
    
    mlflow.set_experiment(experiment_name)
    print(f"MLflow initialization complete")
    return client, experiment_id

def log_model_metrics(metrics_dict):
    """
    Log model metrics to MLflow.
    
    Parameters:
        metrics_dict (dict): Dictionary of metrics to log
    """
    print(f"Logging metrics to MLflow: {metrics_dict}")
    for metric_name, metric_value in metrics_dict.items():
        mlflow.log_metric(metric_name, metric_value)
        print(f"Logged metric: {metric_name} = {metric_value}")

def log_model_params(params_dict):
    """
    Log model parameters to MLflow.
    
    Parameters:
        params_dict (dict): Dictionary of parameters to log
    """
    print(f"Logging parameters to MLflow: {params_dict}")
    for param_name, param_value in params_dict.items():
        mlflow.log_param(param_name, param_value)
        print(f"Logged parameter: {param_name} = {param_value}")

def log_model_artifact(artifact_path, artifact_dir=None):
    """
    Log model artifact to MLflow.
    
    Parameters:
        artifact_path (str): Path to the artifact file
        artifact_dir (str, optional): Directory in MLflow to store the artifact
    """
    print(f"Logging artifact to MLflow: {artifact_path}")
    mlflow.log_artifact(artifact_path, artifact_dir)
    print(f"Logged artifact: {artifact_path}")

def get_mlflow_run_url(experiment_id, run_id):
    """
    Get the URL to view the MLflow run in the UI.
    
    Parameters:
        experiment_id (str): Experiment ID
        run_id (str): Run ID
        
    Returns:
        str: URL to view the run in the MLflow UI
    """
    tracking_uri = mlflow.get_tracking_uri()
    if tracking_uri.startswith("file:"):
        # For local file-based tracking, we need to use localhost
        return f"http://localhost:5000/#/experiments/{experiment_id}/runs/{run_id}"
    else:
        # For remote tracking servers
        return f"{tracking_uri}/#/experiments/{experiment_id}/runs/{run_id}"

def log_sklearn_model(model, model_name="model"):
    """
    Log scikit-learn model to MLflow.
    
    Parameters:
        model: Trained scikit-learn model
        model_name (str): Name to register the model as
    """
    mlflow.sklearn.log_model(model, model_name)
    return model_name
