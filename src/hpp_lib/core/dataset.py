"""Dataset management utilities for HPP_LIB."""

import warnings
from . import utils
from . import io


def _get_val(dct, key):
    """Get value from nested dictionary using dot notation."""
    keys = key.split(".")
    for k in keys:
        dct = dct[k]
    return dct


def _get_uri_from_template(uri_template, kwargs):
    """Get URI from template by substituting variables."""
    return uri_template.format(**kwargs)


def list_datasets(context):
    """List all available datasets in the data catalog."""
    try:
        datasets = context.data_catalog["datasets"]
        return list(datasets.keys())
    except KeyError:
        return []


def load_dataset(context, key, skip=False, **kwargs):
    """Return a dataset from the data_catalog."""
    try:
        ds = _get_val(context.data_catalog["datasets"], key)
        ds["uri"] = _get_uri_from_template(ds["uri"], kwargs)
    except KeyError:
        avlb_keys = list_datasets(context)
        raise ValueError(
            f"Invalid dataset key: {key}. \n\nAvailable datasets: {avlb_keys}"
        )

    load_params = ds.get("driver_params", {}).get("load", {})
    fs = io.fs(context, ds["uri"], ds.get("credential_id"))

    data_uri = fs.glob(ds["uri"])
    df = utils.load_data(data_uri[0], fs=fs, **load_params)
    cols = set(df.columns)
    
    for uri_ in data_uri[1:]:
        try:
            temp_df = utils.load_data(uri_, fs=fs, **load_params)
            if cols != set(temp_df.columns):
                raise ValueError(f"{uri_} columns don't match.")
            df = df.append(temp_df)
        except Exception as e:
            if skip:
                warnings.warn(f"Error: {e}")
                warnings.warn(f"skipping {uri_}")
                continue
            raise e
    return df


def save_dataset(context, df, key, **kwargs):
    """Save a dataset to the data_catalog location."""
    try:
        ds = _get_val(context.data_catalog["datasets"], key)
        ds["uri"] = _get_uri_from_template(ds["uri"], kwargs)
    except KeyError:
        avlb_keys = list_datasets(context)
        raise ValueError(
            f"Invalid dataset key: {key}. \n\nAvailable datasets: {avlb_keys}"
        )
    fs = io.fs(context, ds["uri"], ds.get("credential_id"))
    save_params = ds.get("driver_params", {}).get("save", {})
    return utils.save_data(df, ds["uri"], fs=fs, **save_params)
