"""
Logging utility for the housing value predictor application.
Provides a centralized function to set up and configure logging.
"""
import logging
import sys
from typing import Optional

from housing_value_predictor.config import config


def setup_logging(
    log_level: str = config.DEFAULT_LOG_LEVEL,
    log_file: Optional[str] = None,
    console_log: bool = True,
) -> logging.Logger:
    """Set up logging configuration.

    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Path to log file. If None, no file logging is performed
        console_log: Whether to log to console

    Returns:
        logging.Logger: Configured logger instance
    """
    logger = logging.getLogger("housing_predictor")
    logger.setLevel(logging.getLevelName(log_level.upper()))
    formatter = logging.Formatter(config.DEFAULT_LOG_FORMAT)

    # Remove existing handlers
    logger.handlers = []

    if console_log:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)

    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

    return logger
