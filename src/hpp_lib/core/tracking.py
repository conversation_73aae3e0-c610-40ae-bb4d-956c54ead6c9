"""Core tracking and utilities for HPP_LIB."""

import os
import os.path as op
import warnings
import joblib
import pandas as pd
import numpy as np
from contextlib import contextmanager
from sklearn.compose import ColumnTransformer

# Default artifacts path
DEFAULT_ARTIFACTS_PATH = "artifacts"

# MLflow tracking support
try:
    import mlflow as tracker
    import mlflow.sklearn  # noqa
except ImportError:
    _MLFLOW_SUPPORTED = False
    tracker = None
else:
    _MLFLOW_SUPPORTED = True


def _validate_requirements():
    if not _MLFLOW_SUPPORTED:
        raise RuntimeError("The tracking features require `mlflow` to be installed.")


def _get_tracking_uri(client):
    return client._tracking_client.tracking_uri


def _get_or_create_experiment_id(client, expt_name):
    expt = client.get_experiment_by_name(expt_name)
    if expt is not None:
        expt_id = expt.experiment_id
    else:
        expt_id = client.create_experiment(
            expt_name, artifact_location=client.artifact_uri
        )
    return expt_id


def is_tracker_supported(context):
    """Return ``True`` if context supports using an experiment tracker."""
    try:
        client = context.model_tracker
        return client is not None
    except RuntimeError:
        return False
    else:
        return True


def create_client(cfg):
    """Create a tracking client."""
    _validate_requirements()

    driver = cfg["driver"]
    if driver == "mlflow":
        client = tracker.tracking.MlflowClient(
            tracking_uri=cfg["backend_store"]["uri"],
            registry_uri=cfg["model_registry"]["uri"],
        )
        client.artifact_uri = cfg["artifact_store"]["uri"]
    else:
        raise NotImplementedError()

    return client


@contextmanager
def start_experiment(context, expt_name=None, run_id=None, run_name=None, nested=False):
    """Start an ``Experiment`` and track it using the tracking server."""
    _validate_requirements()

    client = context.model_tracker
    expt_id = _get_or_create_experiment_id(client, expt_name)
    tracker.set_experiment(expt_name)

    # set tracking uri for the context
    old_tracking_uri = tracker.get_tracking_uri()
    tracker.set_tracking_uri(_get_tracking_uri(client))

    # start experiment run
    try:
        with tracker.start_run(
            experiment_id=expt_id, run_id=run_id, run_name=run_name, nested=nested
        ) as _:
            yield tracker
    finally:
        # reset the tracking uri to the value before the start of current context
        tracker.set_tracking_uri(old_tracking_uri)


# Data loading and saving utilities
def load_data(path, **kwargs):
    """Load data from the given path. Type of data is inferred automatically."""
    if path.endswith(".parquet"):
        return pd.read_parquet(path, **kwargs)
    elif path.endswith(".csv"):
        return pd.read_csv(path, **kwargs)
    else:
        raise NotImplementedError(f"File format not supported: {path}")


def save_data(data, path, **kwargs):
    """Save data into the given path. Type of data is inferred automatically."""
    # Ensure directory exists
    os.makedirs(op.dirname(path), exist_ok=True)
    
    if path.endswith(".parquet"):
        if isinstance(data, pd.Series):
            data = pd.DataFrame(data)
        return data.to_parquet(path, **kwargs)
    elif path.endswith(".csv"):
        return data.to_csv(path, **kwargs)
    else:
        raise NotImplementedError(f"File format not supported: {path}")


def load_pipeline(path):
    """Load model pipeline from the `path` specified."""
    return joblib.load(path)


def save_pipeline(pipeline, path):
    """Save model pipeline to the `path` specified."""
    # Ensure directory exists
    os.makedirs(op.dirname(path), exist_ok=True)
    return joblib.dump(pipeline, path)


def get_dataframe(data, columns=None):
    """Convert data to DataFrame with specified columns."""
    if isinstance(data, pd.DataFrame):
        return data
    elif isinstance(data, np.ndarray):
        if columns is not None:
            return pd.DataFrame(data, columns=columns)
        else:
            return pd.DataFrame(data)
    else:
        return pd.DataFrame(data, columns=columns)


def get_feature_names_from_column_transformer(transformer):
    """Get feature names from a ColumnTransformer."""
    if not isinstance(transformer, ColumnTransformer):
        raise ValueError("Input must be a ColumnTransformer")
    
    feature_names = []
    
    for name, trans, columns in transformer.transformers_:
        if name == 'remainder':
            continue
            
        if hasattr(trans, 'get_feature_names_out'):
            # For newer sklearn versions
            try:
                names = trans.get_feature_names_out(columns)
            except:
                names = [f"{name}_{col}" for col in columns]
        elif hasattr(trans, 'get_feature_names'):
            # For older sklearn versions
            try:
                names = trans.get_feature_names(columns)
            except:
                names = [f"{name}_{col}" for col in columns]
        else:
            # Fallback
            names = [f"{name}_{col}" for col in columns]
        
        feature_names.extend(names)
    
    return feature_names


# String cleaning utility
def string_cleaning(series):
    """Clean string data in a pandas Series."""
    if not isinstance(series, pd.Series):
        return series
    
    # Convert to string and clean
    cleaned = series.astype(str).str.strip()
    cleaned = cleaned.str.replace(r'\s+', ' ', regex=True)  # Multiple spaces to single
    cleaned = cleaned.replace('nan', np.nan)  # Convert 'nan' strings back to NaN
    cleaned = cleaned.replace('', np.nan)  # Empty strings to NaN
    
    return cleaned


# Train-test split utility
def custom_train_test_split(df, splitter, by=None):
    """Custom train-test split with optional stratification."""
    if by is not None:
        # Create stratification column
        strat_col = by(df)
        X = df.drop(columns=[strat_col.name] if hasattr(strat_col, 'name') else [])
        y = strat_col
        
        # Perform split
        train_idx, test_idx = next(splitter.split(X, y))
        
        train_df = df.iloc[train_idx].copy()
        test_df = df.iloc[test_idx].copy()
    else:
        # Simple random split
        train_idx, test_idx = next(splitter.split(df))
        train_df = df.iloc[train_idx].copy()
        test_df = df.iloc[test_idx].copy()
    
    return train_df, test_df
