%sql
show tables from demo;
-- select * from demo.titanic_raw;
-- select * from demo.his_tvr;

# M<PERSON>low functions
import os
import glob
import json
import yaml
import mlflow
import pandas as pd
import mlflow.sklearn


def get_latest_file(path):
  list_of_files = glob.glob(path + "*") # * means all if need specific format then *.csv
  latest_file = max(list_of_files, key=os.path.getctime)
  return latest_file

def init_mlflow(expt_name, artifact_location, tracking_uri=None):
    """
    Function to initialize the MLFlow Client for the experiment
    Parameters:
        expt_name (string): name of the mlflow experiment
        tracking_uri (string): mlflow tracking uri
        artifact_location (string): path of mlflow artifacts
    Returns:
        mlflow client & expt_id
    """
    client = mlflow.tracking.MlflowClient(tracking_uri=tracking_uri)
    try:
        expt = client.get_experiment_by_name(expt_name)
        expt_id = expt.experiment_id
    except Exception as e:
        type(e)
        expt_id = client.create_experiment(
            expt_name, artifact_location=artifact_location
        )

    mlflow.set_tracking_uri(tracking_uri)
    mlflow.set_experiment(expt_name)

    return(client, expt_id)

import ta_lib

# AutoML
import numpy as np
import pandas as pd
from tigerml.automl import *

# Reading data
c_data = spark.sql('select * from demo.titanic_raw').toPandas()
c_data['Cabin'] = c_data['Cabin'].fillna(value=np.nan)
c_data['Embarked'] = c_data['Embarked'].fillna(value=np.nan)

# AutoML Config
config = {
  "table_name": "demo.titanic_raw",
  "model_type": "Classification",
  "generations": 30,
  "population_size": 50,
  "name": "titanic",
  "task": TASKS.classification,
  "drop_cols": ['PassengerId', 'Name', 'Ticket'],
  "target": "Survived",
  "train_size": 0.75,
  "remove_na": False
}

# AutoML Run
classifier = AutoML(
 generations=config["generations"],
 population_size=config["population_size"],
 name=config["name"],
 task=config["task"], #use TASKS.regression for regression.
)

# type(classifier)
classifier.prep_data(
 data=c_data.drop(config["drop_cols"], axis=1),
 dv_name=config["target"],
 train_size=config["train_size"],
 remove_na=config["remove_na"]
)
classifier.fit()

# Download this file to local by running following command in local machine, make sure databricks CLI is configured using token
# databricks fs cp dbfs:/FileStore/code-templates/output_reports/<file_name> <target_path_in_local_without_filename/.>
# Example Filename - dbfs:/FileStore/code-templates/output_reports/titanic_structured_classification_at_2021-04-28_15-31-19.html
reports_path = "/dbfs/FileStore/code-templates/output_reports/"
mertrics = classifier.get_report(no_of_pipelines=5, format='.html', reports_path = reports_path)

# MLFlow experiment setup
expt_name = "/Users/<USER>/Code-Templates/AutoML Classifcation"
artifact_location = "dbfs:/FileStore/code-templates/mlflow_artifacts/automl/classification"
client, expt_id = init_mlflow(expt_name, artifact_location)
report_file = get_latest_file(reports_path)

# Register parameters
for key, val in config.items():
  mlflow.log_param(key, val)

# Register Report as artifact
mlflow.log_artifact(report_file, "Report_path")

%sh
pip list

