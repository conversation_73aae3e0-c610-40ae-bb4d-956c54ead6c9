"""
Feature engineering utilities for the housing value predictor.

This module provides custom transformers for feature engineering.
"""
import numpy as np
import pandas as pd
from sklearn.base import BaseEstimator, TransformerMixin


class HousingFeatureEngineer(BaseEstimator, TransformerMixin):
    """
    Custom transformer for engineering housing-related features:
    - Rooms per household
    - Population per household
    - Bedrooms per room (optional)
    """
    def __init__(self, add_bedrooms_per_room=True):
        self.add_bedrooms_per_room = add_bedrooms_per_room
        
    def fit(self, X, y=None):
        return self  # No fitting necessary
        
    def transform(self, X):
        """
        Transform the input data by adding engineered features.
        
        Args:
            X: Input data (numpy array or pandas DataFrame)
            
        Returns:
            numpy array with original and engineered features
        """
        # Convert to numpy array if it's a DataFrame
        if isinstance(X, pd.DataFrame):
            # Get column indices
            total_rooms_ix = X.columns.get_loc("total_rooms") if "total_rooms" in X.columns else None
            total_bedrooms_ix = X.columns.get_loc("total_bedrooms") if "total_bedrooms" in X.columns else None
            population_ix = X.columns.get_loc("population") if "population" in X.columns else None
            households_ix = X.columns.get_loc("households") if "households" in X.columns else None
            X_array = X.values
        else:
            # Assume column order: total_rooms, total_bedrooms, population, households
            # This is a simplification - in production code, you'd want to be more robust
            X_array = X
            total_rooms_ix = 0
            total_bedrooms_ix = 1
            population_ix = 2
            households_ix = 3
            
        # Compute new features
        rooms_per_household = X_array[:, total_rooms_ix] / X_array[:, households_ix]
        population_per_household = X_array[:, population_ix] / X_array[:, households_ix]
        
        if self.add_bedrooms_per_room:
            bedrooms_per_room = X_array[:, total_bedrooms_ix] / X_array[:, total_rooms_ix]
            return np.c_[X_array, rooms_per_household, population_per_household, bedrooms_per_room]
        else:
            return np.c_[X_array, rooms_per_household, population_per_household]
