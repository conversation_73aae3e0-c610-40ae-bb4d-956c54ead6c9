"""
Trains a housing value prediction model.

This module loads training data, preprocesses features, trains a
Random Forest Regressor model, evaluates it, and saves the trained model
and preprocessing artifacts (pipeline).
"""
import argparse
import logging
import os
from typing import Dict, Tuple

import joblib
import mlflow
import numpy as np
import pandas as pd
from sklearn.compose import ColumnTransformer
from sklearn.ensemble import RandomForestRegressor
from sklearn.impute import SimpleImputer
from sklearn.metrics import mean_squared_error
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import OneHotEncoder, StandardScaler

from housing_value_predictor.config import (
    ARTIFACTS_DIR,
    DEFAULT_LOG_LEVEL,
    DEFAULT_MODEL_PARAMS,
    PROCESSED_DATA_DIR,
)
from housing_value_predictor.utils import setup_logging
from housing_value_predictor.utils.feature_engineering import HousingFeatureEngineer


def load_data(input_dir: str, logger: logging.Logger) -> Tuple[pd.DataFrame, pd.Series]:
    """Load training data from a CSV file.

    Assumes the CSV file is named 'train.csv' and is located in `input_dir`.
    The target variable is expected to be 'median_house_value'.

    Args:
        input_dir (str): Directory containing the 'train.csv' file.
        logger (logging.Logger): Logger instance for logging messages.

    Returns:
        Tuple[pd.DataFrame, pd.Series]: A tuple containing:
            - X (pd.DataFrame): DataFrame of features.
            - y (pd.Series): Series of target values ('median_house_value').
    """
    logger.info(f"Loading training data from {input_dir}")
    train_data = pd.read_csv(os.path.join(input_dir, "train.csv"))

    # Split features and target
    X = train_data.drop("median_house_value", axis=1)
    y = train_data["median_house_value"].copy()

    return X, y


def prepare_features(X: pd.DataFrame, logger: logging.Logger) -> Tuple[np.ndarray, Pipeline]:
    """Prepare features for training using a scikit-learn pipeline.

    Steps:
    1. Separates numerical features from categorical ('ocean_proximity').
    2. For numerical features:
       a. Imputes missing values using median strategy.
       b. Adds engineered features (rooms_per_household, etc.).
       c. Standardizes all numerical features.
    3. For categorical features:
       a. One-hot encodes the 'ocean_proximity' feature.
    4. Combines transformed numerical and categorical features.

    Args:
        X (pd.DataFrame): DataFrame of raw features.
        logger (logging.Logger): Logger instance for logging messages.

    Returns:
        Tuple[np.ndarray, Pipeline]: A tuple containing:
            - X_prepared (np.ndarray): Array with prepared features.
            - preprocessing_pipeline (Pipeline): Fitted preprocessing pipeline.
    """
    logger.info("Preparing features using scikit-learn pipeline")

    # Identify numerical and categorical columns
    num_attribs = [col for col in X.columns if col != "ocean_proximity"]
    cat_attribs = ["ocean_proximity"]

    logger.info(f"Numerical features: {num_attribs}")
    logger.info(f"Categorical features: {cat_attribs}")

    # Create preprocessing pipeline for numerical features
    num_pipeline = Pipeline([
        ('imputer', SimpleImputer(strategy="median")),
        ('attribs_adder', HousingFeatureEngineer(add_bedrooms_per_room=True)),
        ('std_scaler', StandardScaler()),
    ])

    # Create full preprocessing pipeline
    preprocessing_pipeline = ColumnTransformer([
        ("num", num_pipeline, num_attribs),
        ("cat", OneHotEncoder(), cat_attribs),
    ])

    # Fit and transform the data
    logger.info("Fitting and transforming features")
    X_prepared = preprocessing_pipeline.fit_transform(X)

    # Get feature names for later use
    num_feature_names = num_attribs.copy()
    # Add engineered feature names
    num_feature_names.extend(["rooms_per_household", "population_per_household", "bedrooms_per_room"])

    # Get one-hot encoded feature names
    cat_encoder = preprocessing_pipeline.named_transformers_["cat"]
    cat_feature_names = cat_encoder.get_feature_names_out(cat_attribs).tolist()

    # Combine all feature names
    feature_names = num_feature_names + cat_feature_names
    logger.info(f"Prepared data shape: {X_prepared.shape}")

    return X_prepared, preprocessing_pipeline


def train_model(
    X: np.ndarray,
    y: pd.Series,
    model_params: Dict,
    logger: logging.Logger
) -> RandomForestRegressor:
    """Train a Random Forest Regressor model.

    Args:
        X (np.ndarray): Array of prepared training features.
        y (pd.Series): Series of training target values.
        model_params (Dict): Dictionary of parameters for RandomForestRegressor.
        logger (logging.Logger): Logger instance for logging messages.

    Returns:
        RandomForestRegressor: Trained scikit-learn RandomForestRegressor model.
    """
    logger.info("Training Random Forest model")
    # Set random_state for reproducibility
    model = RandomForestRegressor(random_state=42, **model_params)
    logger.info(f"Model parameters: {model_params}")
    logger.info(f"Training data shape: {X.shape}")
    model.fit(X, y)
    return model


def evaluate_model(model: RandomForestRegressor, X: np.ndarray, y: pd.Series, logger):
    """Evaluate model performance on the training data.

    Args:
        model (RandomForestRegressor): Trained model to evaluate.
        X (np.ndarray): Array of prepared features.
        y (pd.Series): Series of target values.
        logger (logging.Logger): Logger instance for logging messages.

    Returns:
        float: Root Mean Squared Error (RMSE) of the model.
    """
    predictions = model.predict(X)
    rmse = np.sqrt(mean_squared_error(y, predictions))
    logger.info(f"Model RMSE: {rmse:.2f}")
    return rmse


def main(args):
    # Setup logging
    logger = setup_logging(
        log_level=args.log_level,
        log_file=args.log_path,
        console_log=not args.no_console_log,
    )

    # Set up MLflow tracking
    run_name = "model_training"

    # Convert command line args to a dictionary for easier access
    args_dict = vars(args)
    parent_run_id = args_dict.get('parent_run_id')

    # Log the parent run ID for debugging
    logger.info(f"Parent run ID: {parent_run_id}")

    # Always create a new run
    if parent_run_id:
        # Create a nested run if parent_run_id is provided
        logger.info(f"Starting nested run with parent ID: {parent_run_id}")
        with mlflow.start_run(run_name=run_name, nested=True,
                             tags={"mlflow.parentRunId": parent_run_id}):
            return _run_model_training(args, logger)
    else:
        # Create a standalone run if no parent_run_id
        logger.info("Starting standalone run (no parent ID)")
        with mlflow.start_run(run_name=run_name):
            return _run_model_training(args, logger)


def _run_model_training(args, logger):
    """Execute the model training process with MLflow tracking."""
    # Log MLflow information for debugging
    logger.info(f"MLflow tracking URI: {mlflow.get_tracking_uri()}")
    logger.info(f"MLflow run ID: {mlflow.active_run().info.run_id}")

    # Log parameters
    mlflow.log_param("input_dir", args.input_dir)
    mlflow.log_param("output_dir", args.output_dir)
    mlflow.log_param("script_path", os.path.abspath(__file__))

    # Log model parameters
    for param_name, param_value in DEFAULT_MODEL_PARAMS["random_forest"].items():
        mlflow.log_param(f"model_{param_name}", param_value)

    # Load and prepare data
    X, y = load_data(args.input_dir, logger)
    X_prepared, preprocessing_pipeline = prepare_features(X, logger)

    # Log feature information
    mlflow.log_param("num_features", X_prepared.shape[1])
    mlflow.log_param("num_samples", X_prepared.shape[0])

    # Train model
    model = train_model(X_prepared, y, DEFAULT_MODEL_PARAMS["random_forest"], logger)

    # Evaluate model
    rmse = evaluate_model(model, X_prepared, y, logger)

    # Log metrics
    mlflow.log_metric("train_rmse", rmse)

    # Log feature importances
    if hasattr(model, "feature_importances_"):
        # Get feature names from the pipeline if possible
        num_features = len(model.feature_importances_)
        logger.info(f"Model has {num_features} feature importances")

        # Log each feature importance
        for idx, importance in enumerate(model.feature_importances_):
            # Use a generic feature name since we don't have column names from numpy array
            feature_name = f"feature_{idx}"
            mlflow.log_metric(f"importance_{feature_name}", importance)

    # Save model and preprocessing pipeline
    os.makedirs(args.output_dir, exist_ok=True)
    logger.info(f"Saving model artifacts to {args.output_dir}")

    model_path = os.path.join(args.output_dir, "model.joblib")
    pipeline_path = os.path.join(args.output_dir, "preprocessing_pipeline.joblib")

    joblib.dump(model, model_path)
    joblib.dump(preprocessing_pipeline, pipeline_path)

    # Log artifacts
    mlflow.log_artifact(model_path, "model")
    mlflow.log_artifact(pipeline_path, "preprocessing")

    # Log model to MLflow model registry with more details
    signature = mlflow.models.infer_signature(X_prepared, model.predict(X_prepared[:5]))

    # Create a sample input for the model
    sample_input = X_prepared[:5]

    mlflow.sklearn.log_model(
        model,
        "sklearn_model",
        signature=signature,
        input_example=sample_input,
    )

    # Log additional metrics about the model
    mlflow.log_metric("n_estimators", model.n_estimators)
    mlflow.log_metric("max_features", model.max_features if hasattr(model, "max_features") else 0)
    mlflow.log_metric("n_features_in", model.n_features_in_)

    # Log a summary of the model
    model_info = {
        "model_type": type(model).__name__,
        "n_estimators": model.n_estimators,
        "max_features": str(model.max_features) if hasattr(model, "max_features") else "None",
        "bootstrap": model.bootstrap if hasattr(model, "bootstrap") else "None",
        "n_features_in": model.n_features_in_,
        "num_features": X_prepared.shape[1],
    }

    # Save model info to a JSON file and log it
    import json
    model_info_path = os.path.join(args.output_dir, "model_info.json")
    with open(model_info_path, "w") as f:
        json.dump(model_info, f, indent=2)

    mlflow.log_artifact(model_info_path, "model_metadata")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Train housing price prediction model")
    parser.add_argument(
        "--input-dir",
        type=str,
        default=PROCESSED_DATA_DIR,
        help="Input directory containing processed datasets",
    )
    parser.add_argument(
        "--output-dir",
        type=str,
        default=ARTIFACTS_DIR,
        help="Output directory for model artifacts",
    )
    parser.add_argument(
        "--log-level",
        type=str,
        default=DEFAULT_LOG_LEVEL,
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        help="Set the logging level",
    )
    parser.add_argument(
        "--log-path",
        type=str,
        help="Path to log file. If not specified, logs will only be written to console",
    )
    parser.add_argument(
        "--no-console-log",
        action="store_true",
        help="Disable logging to console",
    )
    parser.add_argument(
        "--parent-run-id",
        dest="parent_run_id",  # Explicitly set the destination attribute name
        type=str,
        help="MLflow parent run ID for nested runs",
    )

    args = parser.parse_args()
    main(args)
