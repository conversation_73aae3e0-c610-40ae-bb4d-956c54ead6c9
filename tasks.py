from invoke import task
import shutil
import sys
import os
import os.path as op

root_dir = op.dirname(op.abspath(__file__))

@task(help={"python": "Python version to use for uv venv (default: 3.12.0)"})
def setup_env(c, python="3.12.0"):
    """
    Sets up the development environment:
    - Ensures `uv` is installed
    - Creates a virtual environment using `uv venv --python <version>` if needed
    - Installs the project in editable mode into the venv
    """
    if shutil.which("uv") is None:
        print("❌ `uv` is not installed. Please install it first: https://github.com/astral-sh/uv")
        sys.exit(1)

    print("✅ `uv` is installed.")

    # Create virtual env if not exists
    if not os.path.isdir(".venv"):
        print(f"🌀 Creating virtual environment with Python {python}...")
        c.run(f"uv venv --python {python}")
    else:
        print("🧪 Found existing virtual environment at `.venv`")

    # Install into the venv using system uv and venv's Python
    print("📦 Installing project in editable mode into `.venv`...")
    c.run("uv pip install -e . --python .venv/bin/python")

    print("✅ Setup complete. Activate your environment using:")
    print("   source .venv/bin/activate   (Linux/macOS)")
    print("   .venv\\Scripts\\activate     (Windows)")


@task
def clean(c):
    """
    Cleans and formats code using ruff:
    - Fixes lint issues
    - Formats code
    """
    print("🧹 Running ruff check --fix...")
    c.run("ruff check . --fix")
    c.run("ruff check . --unsafe-fixes")

    print("🧼 Running ruff format...")
    c.run("ruff format .")
    print("✅ Code cleaned and formatted.")



@task
def build_docker(c):
    """
    Builds Docker image for the housing predictor app.
    """
    dockerfile_path = "deploy/docker/Dockerfile"
    if not os.path.exists(dockerfile_path):
        print(f"❌ Dockerfile not found at: {dockerfile_path}")
        sys.exit(1)

    print("🐳 Building Docker image: housing-predictor:latest...")
    c.run(f"docker build -f {dockerfile_path} -t housing-predictor:latest .")
    print("✅ Docker image built successfully.")
